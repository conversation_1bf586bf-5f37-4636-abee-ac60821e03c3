<script setup lang="ts">
  const route = useRoute()
  const router = useRouter()

  const handleBack = () => {
    router.push('/')
  }
</script>

<template>
  <main class="absolute inset-0 m-auto flex items-center justify-center pb-20">
    <n-result status="500" :title="'500 服务器错误'" description="" size="small">
      <template #footer>
        <n-button @click="handleBack">返回首页</n-button>
      </template>
    </n-result>
  </main>
</template>
