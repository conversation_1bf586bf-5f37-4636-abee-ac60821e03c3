<template>
  <ant-modal :visible="open" :modal-title="formTitle" modalWidth="800" @cancel="cancel" modalHeight="700">
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="年份" prop="year">
              <a-date-picker
                mode="year"
                format="YYYY"
                v-model="form.year"
                placeholder="请选择"
                allow-clear
                :open="yearShowOne"
                style="width: 100%"
                @openChange="openChangeOne"
                @panelChange="panelChangeOne"
              ></a-date-picker>
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="上级下达的年度维修养护资金(万元)" prop="giveCapital">
              <a-input-number
                :precision="2"
                :min="0"
                allowClear
                v-model="form.giveCapital"
                placeholder="请输入"
                style="width: 100%"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="管理单位自筹的年度维修养护资金(万元)" prop="raiseCapital">
              <a-input-number
                :precision="2"
                :min="0"
                allowClear
                v-model="form.raiseCapital"
                placeholder="请输入"
                style="width: 100%"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="实际完成的维修养护资金(万元)" prop="completionCapital">
              <a-input-number
                :precision="2"
                :min="0"
                allowClear
                v-model="form.completionCapital"
                placeholder="请输入"
                style="width: 100%"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="已支付的维修养护资金(万元)" prop="paidCapital">
              <a-input-number
                :precision="2"
                :min="0"
                allowClear
                v-model="form.paidCapital"
                placeholder="请输入"
                style="width: 100%"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="所属工程" prop="projectId">
              <a-tree-select
                v-model="form.projectId"
                style="width: 100%"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                :tree-data="projectOptions"
                show-search
                treeNodeFilterProp="title"
                allowClear
                placeholder="请选择"
                :replaceFields="{
                  children: 'children',
                  title: 'projectName',
                  key: 'projectId',
                  value: 'projectId',
                }"
                tree-default-expand-all
              ></a-tree-select>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <!-- <a-button type="primary" @click="submitForm">保存</a-button> -->
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addCapital, editCapital, getCapitalById } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import moment from 'moment'

  export default {
    name: 'FormDrawer',
    components: { AntModal },
    props: ['projectOptions'],
    data() {
      return {
        loading: false,
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        formTitle: '',
        form: {},
        open: false,
        rules: {
          raiseCapital: [{ required: true, message: '管理单位自筹的年度维修养护资金不能为空', trigger: 'blur' }],
          completionCapital: [{ required: true, message: '实际完成的维修养护资金不能为空', trigger: 'blur' }],
          giveCapital: [{ required: true, message: '上级下达的年度维修养护资金不能为空', trigger: 'blur' }],
          raiseCapital: [{ required: true, message: '管理单位自筹的年度维修养护资金不能为空', trigger: 'blur' }],
          year: [{ required: true, message: '年份不能为空', trigger: 'change' }],
          projectId: [{ required: true, message: '所属工程不能为空', trigger: 'change' }],
          paidCapital: [{ required: true, message: '已支付的维修养护资金不能为空', trigger: 'blur' }],
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 弹出日历和关闭日历的回调
      openChangeOne(status) {
        if (status) {
          this.yearShowOne = true
        }
      },
      // 得到年份选择器的值
      panelChangeOne(value) {
        this.form.year = moment(value).format('YYYY')
        this.yearShowOne = false
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
          this.planDate = []
          this.taskDate = []
        }
      },
      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = '新增'
        if (row != undefined) {
          this.formTitle = '修改'
          getCapitalById({ capitalId: row.capitalId }).then(res => {
            if (res.code == 200) {
              this.form = res.data
              this.form.year = moment(`${res.data.year}`)
              //附件显示
            }
          })
        }
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            this.form.year = moment(this.form.year).format('YYYY')
            if (this.form.capitalId == null) {
              addCapital(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              editCapital(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            }
          }
        })
      },
    },
  }
</script>
<style lang="scss" scoped>
  ::v-deep .ant-modal-body {
    padding: 15px 15px;
    overflow: hidden !important;
  }
  ::v-deep .title {
    font-size: 15px;
    color: #1890ff !important;
  }
</style>
