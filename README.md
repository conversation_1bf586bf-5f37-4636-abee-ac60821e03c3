# FE-water-baseline-onemap

水利一张图



## 技术选型

| 编号  | 技术		  |                        备注  |
| ---- | :-------: |  -------------------------: |
| 1    | Vue       |           前端技术框架 Vue.js |
| 2    | vite      |                  代码编译工具 |
| 3    | Naive UI  |                   前端 UI 库 |
| 4    | sass      |             		层叠样式表语言 |
| 5    | pinia     |   Vue.js 应用程序开发的状态管理 |
| 6    | Axios     |      基于 promise 的网络请求库 |
| 7    | echarts   |                    可视化图表 |
| 8    | vueuse    |                vue3官方hooks |
| 9    | iconify   |               所有流行的图标集 |
| 10   | unocss    |                 原子化css引擎 |

- [Naive UI](https://www.naiveui.com/zh-CN/os-theme/components/button)
- [css转换为unocss对照](https://to-unocss.netlify.app)
- [iconify库](https://icones.js.org/collection/all)  
- [vueuse](https://www.vueusejs.com/guide/)

## 开始使用

1. 环境准备

   - [git](https://git-scm.com/)
   - pnpm version > 9
   - node version > 18

2. 安装

   ```shell
   git clone https://github.com/zhuangqingguo/vue3-admin.git
   ```

3. 本地开发

   配置文件-环境变量

   ```shell
   #.env
   VITE_BASE_API 后端服务访问地址
   VITE_CAS_URL 单点登录地址
   ```


   进入项目根目录

   ```shell
   pnpm install
   ```

   > 若耗时太长可使用阿里镜像源`pnpm config set registry https://registry.npmmirror.com`
   > 还原 `pnpm config set registry https://registry.npmjs.org` 

   启动服务

   ```shell
   pnpm dev
   ```

   > 打开浏览器访问 [http://localhost:5173]

项目打包

```shell
pnpm run build
```

测试环境项目打包

```shell
pnpm run build:test
```

开发环境项目打包

```shell
pnpm run build:dev
```

生产环境项目打包

```shell
pnpm run build:prod
```

## 联系

如果您发现了什么 bug，或者有什么界面建议或意见，请联系xxxx.com
