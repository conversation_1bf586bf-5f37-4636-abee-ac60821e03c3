<template>
  <VxeTable
    ref="vxeTableRef"
    tableTitle="应急抢险"
    :columns="columns"
    :tableData="list"
    :loading="loading"
    :isAdaptPageSize="true"
    @adaptPageSizeChange="adaptPageSizeChange"
    @refresh="getList"
    @selectChange="selectChange"
    @sortChange="sortChange"
    :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
    @handlePageChange="handlePageChange"
  ></VxeTable>
</template>

<script lang="jsx">
  import { getRepairByOId } from '../../services'
  import VxeTable from '@/components/VxeTable'

  export default {
    name: 'MaintenanceRecords',
    props: ['projectId'],
    components: {
      VxeTable,
    },
    data() {
      return {
        loading: false,
        total: 0,
        list: [],
        emergencyTypeOptions: [
          { label: '隐患问题', value: 1 },
          { label: '异常情况', value: 2 },
        ],
        statusOptions: [
          { label: '待处置', value: 1 },
          { label: '已撤回', value: 2 },
          { label: '处置中', value: 3 },
          { label: '待复核', value: 4 },
          { label: '已完成', value: 5 },
        ],
        queryParam: {
          deptId: undefined,
          emergencyType: undefined,
          objectCategoryCode: undefined,
          objectId: this.projectId,
          pageNum: 1,
          pageSize: 10,
          serialNumber: undefined,
          sort: [],
          status: undefined,
        },
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '工单编号',
            field: 'serialNumber',
            minWidth: 120,
          },
          {
            title: '提报单位',
            field: 'deptName',
            minWidth: 110,
          },
          {
            title: '应急类型', 
            minWidth: 70,
            slots: {
              default: ({ row }) => {
                return this.emergencyTypeOptions.find(el => el.value == row.emergencyType)?.label
              },
            },
          },
          {
            title: '应急部位',
            field: 'objectNames',
            minWidth: 150,
          },

          {
            title: '问题描述',
            field: 'content',
            minWidth: 120,
          },
          {
            title: '提报时间',
            field: 'createdTime',
            minWidth: 80,
          },
          {
            title: '处置状态',
            field: 'status',
            minWidth: 60,
            slots: {
              default: ({ row }) => {
                return this.statusOptions.find(el => el.value == row.status)?.label
              },
            },
          },
        ],
      }
    },
    created() {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      /** 查询列表 */
      getList() {
        this.showFormMaintenance = false
        this.loading = true
        getRepairByOId(this.queryParam).then(response => {
          this.list = response?.data?.data

          this.total = response.data.total
          this.loading = false
        })
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.id)
        this.names = valObj.records.map(item => item.serialNumber)
        this.isChecked = !!valObj.records.length
      },
    },
  }
</script>
<style lang="scss" scoped>
  .table-operations {
    .ant-btn {
      &:last-child {
        margin-right: 0px;
      }
    }
  }
</style>
