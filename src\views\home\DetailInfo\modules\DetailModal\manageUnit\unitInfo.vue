<template>
  <div class="unit-info">
    <a-row :gutter="32">
      <a-col :lg="8" :md="8" :sm="24">
        <div class="item">
          <label class="common-label-text">单位名称：</label>
          <span class="common-value-text">{{ data.unitName }}</span>
        </div>
      </a-col>

      <a-col :lg="8" :md="8" :sm="24">
        <div class="item">
          <label class="common-label-text">单位性质：</label>
          <span class="common-value-text">
            {{ propertyOfUnitOptions.find(el => el.value == data.propertyOfUnit)?.label }}
          </span>
        </div>
      </a-col>

      <a-col :lg="8" :md="8" :sm="24">
        <div class="item">
          <label class="common-label-text">单位电话：</label>
          <span class="common-value-text">{{ data.unitTelephone }}</span>
        </div>
      </a-col>

      <a-col :lg="8" :md="8" :sm="24">
        <div class="item">
          <label class="common-label-text">单位传真：</label>
          <span class="common-value-text">{{ data.unitFacsimile }}</span>
        </div>
      </a-col>
      <a-col :lg="8" :md="8" :sm="24">
        <div class="item">
          <label class="common-label-text">汛期值班电话：</label>
          <span class="common-value-text">{{ data.floodTelephone }}</span>
        </div>
      </a-col>
      <a-col :lg="8" :md="8" :sm="24">
        <div class="item">
          <label class="common-label-text">单位职工总数：</label>
          <span class="common-value-text">{{ data.unitTotalNumber }}</span>
        </div>
      </a-col>
      <a-col :lg="8" :md="8" :sm="24">
        <div class="item">
          <label class="common-label-text">单位在岗职工总数：</label>
          <span class="common-value-text">{{ data.unitOnNumber }}</span>
        </div>
      </a-col>
      <a-col :lg="8" :md="8" :sm="24">
        <div class="item">
          <label class="common-label-text">单位责任人姓名：</label>
          <span class="common-value-text">{{ data.unitChargeName }}</span>
        </div>
      </a-col>
      <a-col :lg="8" :md="8" :sm="24">
        <div class="item">
          <label class="common-label-text">单位责任人联系电话：</label>
          <span class="common-value-text">{{ data.unitChargeTelephone }}</span>
        </div>
      </a-col>
      <a-col :lg="8" :md="8" :sm="24">
        <div class="item">
          <label class="common-label-text">单位责任人联系手机：</label>
          <span class="common-value-text">{{ data.unitChargePhone }}</span>
        </div>
      </a-col>
      <a-col :lg="8" :md="8" :sm="24">
        <div class="item">
          <label class="common-label-text">管理单位编制总数：</label>
          <span class="common-value-text">{{ data.preparationsNumber }}</span>
        </div>
      </a-col>
      
      <a-col :lg="8" :md="8" :sm="24">
        <div class="item">
          <label class="common-label-text">管理委员会名称：</label>
          <span class="common-value-text">{{ data.committeeName }}</span>
        </div>
      </a-col>
      <a-col :lg="8" :md="8" :sm="24">
        <div class="item">
          <label class="common-label-text">安全监管部门：</label>
          <span class="common-value-text">{{ data.committeeDept }}</span>
        </div>
      </a-col>
      <a-col :lg="8" :md="8" :sm="24">
        <div class="item">
          <label class="common-label-text">防汛责任人姓名：</label>
          <span class="common-value-text">{{ data.floodChargeName }}</span>
        </div>
      </a-col>
      <a-col :lg="8" :md="8" :sm="24">
        <div class="item">
          <label class="common-label-text">防汛责任人职务：</label>
          <span class="common-value-text">{{ data.floodChargeJob }}</span>
        </div>
      </a-col>
      <a-col :lg="8" :md="8" :sm="24">
        <div class="item">
          <label class="common-label-text">单位地址：</label>
          <span class="common-value-text">{{ data.unitAddress }}</span>
        </div>
      </a-col>
      <a-col :lg="24" :md="24" :sm="24">
        <div class="item">
          <label class="common-label-text">上级水行政管理部门：</label>
          <span class="common-value-text">{{ data.lastDept }}</span>
        </div>
      </a-col>
      <a-col :lg="24" :md="24" :sm="24">
        <label class="common-label-text">所属工程：</label>
      </a-col>
      <a-col :lg="24" :md="24" :sm="24">
        <div class="common-text-item">
          <span class="common-value-text">{{ data.projectName }}</span>
        </div>
      </a-col>

      <a-col :lg="24" :md="24" :sm="24" :span="24">
        <div style="margin-bottom: 20px">
          <label class="common-label-text">单位批文：</label>
          <div class="file-item" v-for="(el, i) in data.letterAttaches" :key="i" @click="() => downLoad(el.attachUrl)">
            <a-icon type="paper-clip" />
            <div class="file-name" style="margin-left: 5px">{{ el.attachName }}</div>
          </div>
        </div>
      </a-col>
      <a-col :lg="24" :md="24" :sm="24" :span="24">
        <div>
          <label class="common-label-text">管理单位联系清单：</label>
          <div class="file-item" v-for="(el, i) in data.contactAttaches" :key="i" @click="() => downLoad(el.attachUrl)">
            <a-icon type="paper-clip" />
            <div class="file-name" style="margin-left: 5px">{{ el.attachName }}</div>
          </div>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="jsx">
  import { getUnitPage } from './services.js'
  import { getOptions } from '@/api/common.js'

  export default {
    name: 'UnitInfo',
    props: {
      projectId: {},
    },
    data() {
      return {
        propertyOfUnitOptions: [],

        data: {},
      }
    },
    created() {
      getOptions('propertyOfUnit').then(res => {
        this.propertyOfUnitOptions = res.data.map(el => ({ label: el.value, value: el.key }))
      })

      getUnitPage({ projectId: this.projectId, pageNum: 1, pageSize: 1 }).then(res => {
        this.data = res.data?.data?.[0] || {}
      })
    },
    methods: {
      downLoad(url) {
        window.open(url)
      },
    },
  }
</script>

<style lang="less" scoped>
  @import url('~@/global.less');

  .unit-info {
    padding: 0 30px;
  }

  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .file-item {
    display: flex;
    align-items: center;
    line-height: 25px;
  }

  .file-name {
    color: @primary-color;
    cursor: pointer;
    margin-right: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
</style>
