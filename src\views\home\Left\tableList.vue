<template>
  <div class="flex-1 flex-col h-full p-0.16rem overflow-hidden">
    <div class="flex-center-between mb-0.16rem font-[SourceHanSansCN-Medium]">
      <div>{{ attrs.selected?.objectCategoryName }}</div>
      <n-button type="primary" @click="handleExport" :loading="state.exportLoading">
        <template #icon>
          <MyIcon name="export" class="text-0.18rem" />
        </template>
        导出
      </n-button>
    </div>

    <n-input v-model:value="state.keywords" @update:value="debounceGetList" placeholder="请输入名称" class="mb-0.12rem">
      <template #suffix>
        <MyIcon name="search" class="text-0.18rem" />
      </template>
    </n-input>

    <div class="flex-1" v-resize="debounce(onTableResize, 500)">
      <n-data-table
        class="h-full"
        remote
        :columns="columns"
        :loading="state.loading"
        :row-key="(row: any) => row.code"
        :row-props="
          (rowData, rowIndex) => ({
            onClick: () => handleRowClick(rowData, rowIndex),
          })
        "
        :data="state.list"
        :bordered="false"
        :pagination="state.pagination"
        @update:page="handlePageChange"
        :scroll-x="state.scrollX"
        :scrollbar-props="{ trigger: 'none' }"
      >
        <template #empty>
          <MyEmpty v-show="state.list.length === 0" :src="getImageUrl('empty1.png')" description="暂无数据" />
        </template>
      </n-data-table>
    </div>
  </div>
</template>
<script setup lang="tsx" name="TableList">
  import { debounce } from 'lodash-es'
  import dayjs from 'dayjs'
  import { objectCategoryObjectPage } from '../services'
  import excelExport from '@/utils/excelExport'

  const attrs = useAttrs()
  const activeItem = defineModel('activeItem')

  const state = $ref({
    keywords: null,
    classFieldCodes: null,
    loading: false,
    list: [],
    pagination: {
      size: 'small',
      pageSlot: 5,
      page: 1,
      pageSize: 20,
      itemCount: 0,
      prefix: ({ itemCount }) => `共${itemCount}条`,
    },

    scrollX: '0rem',

    exportLoading: false,
  })

  const columns = computed(() => [
    {
      title: '序号',
      width: '0.55rem',
      className: 'index-order',
      fixed: 'left',
      align: 'center',
      render: (row, index) => index + 1,
    },
    {
      title: '名称',
      key: 'name',
      fixed: 'left',
      minWidth: '1.2rem',
      render: (row, index) => row.name,
      ellipsis: { tooltip: true },
    },
    {
      title: attrs.selected?.classField,
      key: 'classFieldName',
      minWidth: '0.6rem',
      ellipsis: { tooltip: true },
    },
    ...(attrs.selected?.indexFields || [])?.map(el => ({
      title: `${el.fieldName}${el.unit ? `(${el.unit})` : ''}`,
      key: el.fieldKey,
      align: 'center',
      width: el.fieldName.length * 0.15 + 0.5 + 'rem',
      render: (row, index) => {
        const obj = row.indexFields?.find(ele => ele.fieldKey === el.fieldKey)
        return (
          <span
            class='font-bold'
            style={{
              color: obj?.isOnline ? '#00B487' : 'rgba(0, 180, 135, 0.3)',
            }}
          >
            {obj?.fieldValue === 0 ? 0 : obj?.fieldValue || '-'}
          </span>
        )
      },
      ellipsis: { tooltip: true },
    })),
  ])

  watch(
    () => attrs.selected,
    newVal => {
      state.keywords = null
      state.pagination.page = 1
      state.classFieldCodes = attrs.allData?.[attrs.tabLevel1]
        ?.find(el => el.objectCategoryCode === attrs.selected.objectCategoryCode)
        ?.items.filter(el => el.checked)
        ?.map(el => el.code)
      getList()
    },
  )

  watch(
    () => attrs.allData,
    newVal => {
      if (newVal.objectCategoryCode === attrs.selected.objectCategoryCode) {
        state.classFieldCodes = newVal?.[attrs.tabLevel1]
          .find(el => el.objectCategoryCode === attrs.selected.objectCategoryCode)
          .items.filter(el => el.checked)
          .map(el => el.code)

        state.pagination.page = 1
        getList()
      }
    },
  )

  const getList = async () => {
    if (!attrs.selected?.objectCategoryCode) return
    state.loading = true

    await nextTick()
    objectCategoryObjectPage({
      isDataMode: attrs.isDataMode,
      objectCategoryCode: attrs.selected.objectCategoryCode,
      keywords: state.keywords,
      classFieldCodes: state.classFieldCodes,
      pageNum: state.pagination.page,
      pageSize: state.pagination.pageSize,
    }).then(res => {
      state.loading = false
      state.list = res.data.data || []
      state.pagination.itemCount = res.data.total

      // 设置scrollX
      nextTick(() => {
        if (attrs.isDataMode) {
          state.scrollX =
            columns.value.reduce((acc, cur) => acc + +(cur?.width || cur?.minWidth).replace('rem', ''), 0).toFixed(2) + 'rem'
        } else {
          state.scrollX = '0rem'
        }
      })
    })
  }
  const debounceGetList = debounce(() => getList(), 500)

  const onTableResize = ({ contentRect }) => {
    const itemHeight = document.querySelector('.n-data-table-thead').offsetHeight
    const pageHeight = document.querySelector('.n-data-table__pagination').offsetHeight
    state.pagination.pageSize = Math.floor((contentRect.height - pageHeight - itemHeight) / itemHeight)

    if (!state.pagination.pageSize) return
    getList()
  }

  const handlePageChange = currentPage => {
    state.pagination.page = currentPage
    getList()
  }

  const handleRowClick = (rowData, rowIndex) => {
    if (activeItem.value?.id === rowData.id) {
      activeItem.value = { gisLayer: activeItem.value.gisLayer }
      return
    }
    activeItem.value = {
      ...rowData,
      tabVal1: attrs.tabLevel1,
      tabVal2: attrs.tabLevel2,
      isLeftSource: true,
      gisLayer: attrs.selected?.gisLayer,
    }
  }

  const handleExport = () => {
    state.exportLoading = true

    objectCategoryObjectPage({
      isDataMode: attrs.isDataMode,
      objectCategoryCode: attrs.selected.objectCategoryCode,
      keywords: state.keywords,
      classFieldCodes: state.classFieldCodes,
      pageNum: 1,
      pageSize: Number.MAX_SAFE_INTEGER,
    }).then(res => {
      state.exportLoading = false
      const listData = res.data.data || []

      listData.forEach(el => {
        attrs.selected?.indexFields.forEach(ele => {
          const val = el.indexFields?.find(item => item.fieldKey === ele.fieldKey)?.fieldValue
          el[ele.fieldKey] = val === null || val === undefined ? '-' : val
        })
      })

      excelExport(
        columns.value.slice(1, columns.value.length).map(el => ({ ...el, field: el.key })),
        listData,
        `${attrs.selected?.objectCategoryName}${dayjs().format('YYYYMMDDHHmmss')}`,
      )
    })
  }
</script>
<style lang="scss" scoped>
  :deep(.n-button) {
    font-size: 0.14rem;
    padding: 0 0.14rem;
    height: 0.32rem;
    .n-button__icon {
      margin-right: 0.06rem;
      height: 0.18rem;
      width: 0.18rem;
    }
  }
  :deep(.n-input) {
    font-size: 0.14rem;
    background: #f2f3f5;

    .n-input-wrapper {
      padding: 0 0.12rem;
      .n-input__input-el {
        height: 0.32rem;
        line-height: 1.1;
      }
    }

    .n-input__border {
      border-color: transparent;
    }
  }

  // 表格
  :deep(.n-data-table) {
    font-size: 0.14rem;
    .n-data-table-wrapper {
      // flex: 1;
      // overflow: hidden;

      .n-data-table-base-table {
        height: 100%;
        // display: flex;
        // flex-direction: column;
        position: relative;
        .n-data-table-empty {
          // flex: 1;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          .n-empty__description {
            font-size: 0.14rem;
          }
        }
      }
      .n-data-table-th {
        padding: 0.1rem 0.05rem;
        background-color: #ffffff;
        font-family: SourceHanSansCN-Medium;
      }
      .n-data-table-td {
        padding: 0.1rem 0.05rem;
      }
    }
  }

  // 分页
  :deep(.n-data-table__pagination) {
    padding-top: 0.12rem;
    margin: 0;

    .n-pagination {
      font-size: 0.12rem;
      .n-pagination-item {
        min-width: 0.24rem;
        height: 0.24rem;
        padding: 0;
        margin-left: 0.08rem;
      }
    }
  }
</style>
