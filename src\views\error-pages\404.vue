<script setup lang="ts">
  const router = useRouter()

  const handleBack = () => {
    router.replace('/')
  }
</script>

<template>
  <main class="absolute inset-0 m-auto flex items-center justify-center pb-20">
    <n-result status="404" :title="'404 您访问的页面不存在'" description="" size="small">
      <template #footer>
        <n-button @click="handleBack">返回首页</n-button>
      </template>
    </n-result>
  </main>
</template>
