<template>
  <div class="training">
    <div class="top">
      <div class="chart-item">
        <div class="chart-title">年度培训数量排名</div>
        <YearBarChart :dataSource="rankData" />
      </div>
      <div class="chart-item">
        <div class="chart-title">
          月度培训数量趋势分析
          <a-date-picker
            format="YYYY"
            valueFormat="YYYY"
            v-model="year"
            @panelChange="onYearChange"
            mode="year"
            :open="open"
            @focus="open = true"
          >
            <div slot="renderExtraFooter" slot-scope="mode" style="text-align: right">
              <a-button type="link" @click="open = false">关闭</a-button>
            </div>
          </a-date-picker>
        </div>
        <MonthLineChart :dataSource="lineData" />
      </div>
    </div>

    <div class="chart-title">年度培训统计</div>
    <div class="table-box">
      <VxeTable
        size="mini"
        ref="vxeTableRef"
        @sortChange="sortChange"
        :isShowTableHeader="false"
        :columns="columns"
        :tableData="listData"
        :loading="false"
        :tablePage="false"
      ></VxeTable>
    </div> 

    <YearTrainingDetail v-if="showYearDetailModal" ref="yearDetailModalRef" @close="showYearDetailModal = false" />
  </div>
</template>

<script lang="jsx">
  import YearBarChart from './modules/yearBarChart.vue'
  import MonthLineChart from './modules/monthLineChart.vue'
  import { getYearTrainingList, getMonthTrainingList } from './services.js'
  import moment from 'moment'
  import VxeTable from '@/components/VxeTable/index.vue'
  import YearTrainingDetail from './modules/yearTrainingDetail.vue'

  export default {
    name: 'TrainingInfo',
    props: {
      projectId: {}
    },
    components: {
      YearBarChart,
      MonthLineChart,
      VxeTable,
      YearTrainingDetail
    },
    data() {
      return {
        showYearDetailModal: false,

        rankData: [],
        lineData: [],

        year: moment().format('YYYY'),
        open: false,
        sort: [],

        listData: [],
        columns: [
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '年度',
            field: 'year',
            minWidth: 100
          },
          {
            title: '年度培训数量(个)',
            field: 'yearTrainingCount',
            minWidth: 160,
            sortable: true,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <a-button type='link' onClick={() => this.onDetail(row)}>
                    {row.yearTrainingCount}
                  </a-button>
                )
              }
            }
          },
          {
            title: '培训经费(元)',
            field: 'yearActualExpense',
            minWidth: 150,
            sortable: true
          }
        ]
      }
    },
    created() {
      this.getList('init')
      this.onYearChange()
    },
    methods: {
      onYearChange(val) {
        if (val) {
          this.year = val.format('YYYY')
          this.open = false
        }

        getMonthTrainingList({ projectId: this.projectId, year: this.year }).then(res => {
          this.lineData = res.data
        })
      },

      // 排序
      sortChange(valObj) {
        this.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },

      getList(type) {
        getYearTrainingList({ projectId: this.projectId, sort: this.sort }).then(res => {
          this.listData = res.data
          if (type == 'init') {
            this.rankData = res.data.sort((a, b) => a.yearTrainingCount > b.yearTrainingCount).slice(0, 3)
          }
        })
      },

      onDetail(row) {
        this.showYearDetailModal = true
        this.$nextTick(() => this.$refs.yearDetailModalRef.handleDetail({ ...row, projectId: this.projectId }))
      }
    }
  }
</script>

<style lang="scss" scoped>
  .training {
    height: 100%;
    display: flex;
    flex-direction: column;
    .top {
      display: flex;
      .chart-item {
        flex: 1;
      }
    }
    .chart-title {
      padding: 0 20px;
      display: flex;
      justify-content: space-between;
      height: 30px;
      align-items: center;
    }

    .table-box {
      flex: 1;
      position: relative;
    }
  }
</style>
