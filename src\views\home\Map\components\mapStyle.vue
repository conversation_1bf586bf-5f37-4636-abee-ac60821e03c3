<template>
  <div class="map-style" :style="{ right: attrs.isShow.isShowRight ? '3.54rem' : '0.2rem' }">
    <div
      class="style-item item-common"
      v-for="(el, idx) in mapStyles.filter(el => el.label !== activeMapStyle?.label)"
      :style="{
        backgroundImage: `url(${el.bg})`,
        display: 'none',
      }"
      @click="onStyleItemClick(el, idx)"
    >
      <div class="label">{{ el.label }}</div>
    </div>

    <div
      class="item-common b-(2px solid #ffffff)"
      :style="{ backgroundImage: `url(${mapStyles.find(el => el.label === activeMapStyle?.label)?.bg})` }"
    >
      <div class="label">{{ activeMapStyle?.label }}</div>
    </div>
  </div>
</template>
<script setup lang="tsx" name="MapStyle">
  import { switchMapStyle, clearSourceAndLayer } from '../utils/mapUtils.js'

  const attrs = useAttrs()

  const mapStyles = [
    {
      label: '地形',
      bg: getImageUrl('map-terrain.png'),
      setBaseMapStyle: () => {
        clearSourceAndLayer(attrs.mapIns, ['mapbox-wmts-base-layer'], ['mapbox-wmts-base-layer'])

        attrs.mapIns.addLayer(
          {
            id: 'mapbox-wmts-base-layer',
            type: 'raster',
            source: {
              type: 'raster',
              tiles: [
                `${import.meta.env.VITE_TIANDI_BASE}/ter_w/wmts?tk=${import.meta.env.VITE_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=ter&tilematrix={z}&tilerow={y}&tilecol={x}`,
              ],
              tileSize: 256,
            },
          },
          attrs.mapIns.getLayer('mapbox-wmts-label-layer') ? 'mapbox-wmts-label-layer' : attrs.mapIns.getStyle().layers[0].id,
        )
      },
      setMapLabel: () => {
        clearSourceAndLayer(attrs.mapIns, ['mapbox-wmts-label-layer'], ['mapbox-wmts-label-layer'])

        attrs.isShow.isShowLabel &&
          attrs.mapIns.addLayer(
            {
              id: 'mapbox-wmts-label-layer',
              type: 'raster',
              source: {
                type: 'raster',
                tiles: [
                  `${import.meta.env.VITE_TIANDI_BASE}/cta_w/wmts?tk=${import.meta.env.VITE_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=cta&tilematrix={z}&tilerow={y}&tilecol={x}`,
                ],
                tileSize: 256,
              },
            },
            attrs.mapIns.getStyle().layers[1].id,
          )
      },
    },
    {
      label: '卫星图',
      bg: getImageUrl('map-satellite.png'),
      setBaseMapStyle: () => {
        clearSourceAndLayer(attrs.mapIns, ['mapbox-wmts-base-layer'], ['mapbox-wmts-base-layer'])

        attrs.mapIns.addLayer(
          {
            id: 'mapbox-wmts-base-layer',
            type: 'raster',
            source: {
              type: 'raster',
              tiles: [
                `${import.meta.env.VITE_TIANDI_BASE}/img_w/wmts?tk=${import.meta.env.VITE_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=img&tilematrix={z}&tilerow={y}&tilecol={x}`,
              ],
              tileSize: 256,
            },
          },
          attrs.mapIns.getLayer('mapbox-wmts-label-layer') ? 'mapbox-wmts-label-layer' : attrs.mapIns.getStyle().layers[0].id,
        )
      },
      setMapLabel: () => {
        clearSourceAndLayer(attrs.mapIns, ['mapbox-wmts-label-layer'], ['mapbox-wmts-label-layer'])

        attrs.isShow.isShowLabel &&
          attrs.mapIns.addLayer(
            {
              id: 'mapbox-wmts-label-layer',
              type: 'raster',
              source: {
                type: 'raster',
                tiles: [
                  `${import.meta.env.VITE_TIANDI_BASE}/cia_w/wmts?tk=${import.meta.env.VITE_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=cia&tilematrix={z}&tilerow={y}&tilecol={x}`,
                ],
                tileSize: 256,
              },
            },
            attrs.mapIns.getStyle().layers[1].id,
          )
      },
    },
    {
      label: '地图',
      bg: getImageUrl('map-street.png'),
      setBaseMapStyle: () => {
        clearSourceAndLayer(attrs.mapIns, ['mapbox-wmts-base-layer'], ['mapbox-wmts-base-layer'])

        attrs.mapIns.addLayer(
          {
            id: 'mapbox-wmts-base-layer',
            type: 'raster',
            source: {
              type: 'raster',
              tiles: [
                `${import.meta.env.VITE_TIANDI_BASE}/vec_w/wmts?tk=${import.meta.env.VITE_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=vec&tilematrix={z}&tilerow={y}&tilecol={x}`,
              ],
              tileSize: 256,
            },
          },
          attrs.mapIns.getLayer('mapbox-wmts-label-layer') ? 'mapbox-wmts-label-layer' : attrs.mapIns.getStyle().layers[0].id,
        )
      },
      setMapLabel: () => {
        clearSourceAndLayer(attrs.mapIns, ['mapbox-wmts-label-layer'], ['mapbox-wmts-label-layer'])

        attrs.isShow.isShowLabel &&
          attrs.mapIns.addLayer(
            {
              id: 'mapbox-wmts-label-layer',
              type: 'raster',
              source: {
                type: 'raster',
                tiles: [
                  `${import.meta.env.VITE_TIANDI_BASE}/cva_w/wmts?tk=${import.meta.env.VITE_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=cva&tilematrix={z}&tilerow={y}&tilecol={x}`,
                ],
                tileSize: 256,
              },
            },
            attrs.mapIns.getStyle().layers[1].id,
          )
      },
    },
  ]

  let activeMapStyle = $ref(null)

  watch(
    () => attrs.isShow.isShowLabel,
    newVal => {
      nextTick(() => {
        activeMapStyle.setMapLabel()
      })
    },
  )

  const onStyleItemClick = (el, idx) => {
    activeMapStyle = el

    el.setMapLabel()
    el.setBaseMapStyle()
  }

  const onMapStyleMounted = () => {
    onStyleItemClick(mapStyles[1], 1)
  }
  defineExpose({ onMapStyleMounted })
</script>
<style lang="scss" scoped>
  .map-style {
    position: absolute;
    bottom: 0.2rem;
    right: 3.54rem;
    z-index: 99;
    border-radius: 0.04rem;
    display: flex;

    padding: 0.02rem;
    color: #ffffff;

    &:hover {
      &::before {
        position: absolute;
        content: '';
        border-radius: 0.04rem;
        width: calc(100% + 0.2rem);
        height: calc(100% + 0.2rem);
        background: rgba(255, 255, 255, 0.8);
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }

      .style-item {
        display: block !important;
      }
    }

    .item-common {
      width: 1.08rem;
      height: 0.66rem;
      position: relative;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      border-radius: 0.04rem;
      user-select: none;
      cursor: pointer;

      .label {
        position: absolute;
        right: 0;
        bottom: 0;
        font-size: 0.12rem;
        padding: 0.04rem;
        background-color: $primary-color;
        border-radius: 0.02rem;
      }
    }

    .style-item {
      margin-right: 0.1rem;
      border: 2px solid #ffffff;
    }
  }
</style>
