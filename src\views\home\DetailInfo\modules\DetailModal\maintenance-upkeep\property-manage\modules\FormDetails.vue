<template>
  <ant-modal :visible="open" :modal-title="formTitle" modalWidth="600" @cancel="cancel" modalHeight="500">
    <div slot="content">
      <a-form-model ref="form" :model="form">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">基本信息</div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">物业或外委单位：</label>
              <span class="common-value-text">{{ form.propertyName }}</span>
            </div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">合同名称：</label>
              <span class="common-value-text">{{ form.contractName }}</span>
            </div>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">开始时间：</label>
              <span class="common-value-text">{{ form.startTime }}</span>
            </div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">结束时间：</label>
              <span class="common-value-text">{{ form.endTime }}</span>
            </div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">资金(万元)：</label>
              <span class="common-value-text">{{ form.propertyCapital }}</span>
            </div>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">所属工程：</label>
              <span class="common-value-text">{{ form.projectName }}</span>
            </div>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <div style="margin-bottom: 20px">
              <div class="title">合同文件附件</div>

              <div
                class="file-item"
                v-for="(el, i) in form.contractAttaches"
                :key="i"
                @click="() => downLoad(el.attachUrl)"
              >
                <a-icon type="paper-clip" />
                <div class="file-name" style="margin-left: 5px">{{ el.attachName }}</div>
              </div>
            </div>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <div style="margin-bottom: 20px">
              <div class="title">阶段计划附件</div>
              <div
                class="file-item"
                v-for="(el, i) in form.stageAttaches"
                :key="i"
                @click="() => downLoad(el.attachUrl)"
              >
                <a-icon type="paper-clip" />
                <div class="file-name" style="margin-left: 5px">{{ el.attachName }}</div>
              </div>
            </div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <div style="margin-bottom: 20px">
              <div class="title">月度考核报告附件</div>

              <div
                class="file-item"
                v-for="(el, i) in form.monthlyAttaches"
                :key="i"
                @click="() => downLoad(el.attachUrl)"
              >
                <a-icon type="paper-clip" />
                <div class="file-name" style="margin-left: 5px">{{ el.attachName }}</div>
              </div>
            </div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <div style="margin-bottom: 20px">
              <div class="title">季度考核报告附件</div>

              <div
                class="file-item"
                v-for="(el, i) in form.quarterlyAttaches"
                :key="i"
                @click="() => downLoad(el.attachUrl)"
              >
                <a-icon type="paper-clip" />
                <div class="file-name" style="margin-left: 5px">{{ el.attachName }}</div>
              </div>
            </div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <div style="margin-bottom: 20px">
              <div class="title">验收材料附件</div>

              <div
                class="file-item"
                v-for="(el, i) in form.acceptanceAttaches"
                :key="i"
                @click="() => downLoad(el.attachUrl)"
              >
                <a-icon type="paper-clip" />
                <div class="file-name" style="margin-left: 5px">{{ el.attachName }}</div>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getMaintenanceById } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import moment from 'moment'

  export default {
    name: 'FormDetails',
    components: { AntModal },
    props: ['projectOptions', 'inspectionTypeOptions', 'propertyList'],
    data() {
      return {
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        formTitle: '',
        form: {},
        open: false,
        rules: {},
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 物业或外委单位格式化
      propertyFormat(value) {
        if (value) {
          return this.propertyList.find(item => item.projectId == value)?.propertyName
        }
      },
      // 检查结果格式化
      inspectionStatusFormat(value) {
        if (value) {
          return this.inspectionStatusOptions.find(item => item.key == value)?.value
        } else {
          return ''
        }
      },
      // 检查类型格式化
      inspectionTypeFormat(value) {
        if (value) {
          return this.inspectionTypeOptions.find(item => item.key == value)?.value
        } else {
          return ''
        }
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
          this.planDate = []
          this.taskDate = []
        }
      },

      /** 新增按钮操作 */
      details(row) {
        this.open = true
        if (row != undefined) {
          this.formTitle = '查看'
          getMaintenanceById({ maintenancePropertyId: row.maintenancePropertyId }).then(res => {
            if (res.code == 200) {
              this.form = res.data
              this.form.inspectionType = String(this.form.inspectionType)
              //附件显示
            }
          })
        }
      },

      downLoad(url) {
        window.open(url)
      },
    },
  }
</script>
<style lang="less" scoped>
  @import url('~@/global.less');
  .title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
  }
  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .file-item {
    display: flex;
    align-items: center;
    line-height: 25px;
  }

  .file-name {
    color: @primary-color;
    cursor: pointer;
    margin-right: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
</style>
