<template>
  <div class="manage-unit">
    <a-tabs v-model="tabKey" size="small">
      <a-tab-pane key="1" tab="组织机构">
        <OrgDept :projectId="projectId" :projectName="projectName" />
      </a-tab-pane>
      <a-tab-pane key="2" tab="培训资料">
        <TrainingInfo :projectId="projectId" />
      </a-tab-pane>
      <a-tab-pane key="3" tab="单位信息">
        <UnitInfo :projectId="projectId" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="jsx">
  import OrgDept from './orgDept.vue'
  import TrainingInfo from './trainingInfo.vue'
  import UnitInfo from './unitInfo.vue'

  export default {
    name: 'CustodialFund',
    props: {
      projectId: {},
      projectName: {},
    },
    components: {
      OrgDept,
      TrainingInfo,
      UnitInfo,
    },
    data() {
      return {
        tabKey: '1',
      }
    },
    computed: {},
    watch: {},
    created() {},
    methods: {},
  }
</script>
<style lang="less" scoped>
  .manage-unit {
    margin: 10px 0;
    width: 100%;
    height: calc(100% - 20px);
    overflow: hidden;
  }

  ::v-deep .ant-tabs-top {
    height: 100%;
  }
  ::v-deep .ant-tabs-content {
    height: calc(100% - 50px) !important;
  }
  ::v-deep .ant-tabs-tabpane {
  }
</style>
