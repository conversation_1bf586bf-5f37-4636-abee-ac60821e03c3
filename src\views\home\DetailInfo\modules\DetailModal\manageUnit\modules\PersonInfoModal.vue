<template>
  <!-- 增加修改 -->
  <ant-modal :visible="open" :modal-title="modalTitle" modalWidth="600" @cancel="cancel" modalHeight="800">
    <div slot="content">
      <a-row :gutter="32">
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">姓名：</label>
            <span class="common-value-text">{{ data.personnelName }}</span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">身份证号：</label>
            <span class="common-value-text">{{ data.idNo }}</span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">学历：</label>
            <span class="common-value-text">{{ data.education }}</span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">专业：</label>
            <span class="common-value-text">{{ data.major }}</span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">联系方式：</label>
            <span class="common-value-text">{{ data.telephone }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">岗位：</label>
            <span class="common-value-text">{{ data.positionName }}</span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="item">
            <label class="common-label-text">编制情况：</label>
            <span class="common-value-text">
              {{ compilationOptions.find(el => el.value == data.statusEstablishment)?.label }}
            </span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="item">
            <label class="common-label-text">职务：</label>
            <span class="common-value-text">{{ data.jobDescription }}</span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="item">
            <label class="common-label-text">是否在岗：</label>
            <span class="common-value-text">{{ data.isJob == 1 ? '是' : '否' }}</span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="item">
            <label class="common-label-text">是否岗位负责人：</label>
            <span class="common-value-text">{{ data.isCharge == 1 ? '是' : '否' }}</span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24">
          <div class="item">
            <label class="common-label-text">所属工程：</label>
            <span class="common-value-text">{{ data.projectName }}</span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="item">
            <label class="common-label-text">个人照片：</label>
          </div>
          <UploadFile
            :fileUrl="data.photoAttaches?.map(el => el.attachUrl)"
            :multiple="true"
            listType="picture-card"
            :onlyView="true"
            disabled
          />
        </a-col>

        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="item">
            <label class="common-label-text">证书附件：</label>
            <div
              class="file-item"
              v-for="(el, i) in data.certificateAttaches"
              :key="i"
              @click="() => downLoad(el.attachUrl)"
            >
              <a-icon type="paper-clip" />
              <div class="file-name" style="margin-left: 5px">{{ el.attachName }}</div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getPersonnel } from '../services'
  import moment from 'moment'
  import AntModal from '@/components/pt/dialog/AntModal'
  import UploadFile from '@/components/UploadFile/index.vue'

  export default {
    name: 'DetailModal',
    props: [],
    components: { AntModal, UploadFile },
    data() {
      return {
        terminalStatusOptions: [],

        open: false,
        modalTitle: '',
        data: {},

        compilationOptions: [
          { label: '在编', value: 1 },
          { label: '编外', value: 2 },
        ],
      }
    },
    filters: {},
    created() {},
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      /** 查看按钮操作 */
      handleDetail(record) {
        this.open = true
        this.modalTitle = '查看'
        this.data = record
      },
    },
  }
</script>

<style lang="scss" scoped>
 

  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .file-item {
    display: flex;
    align-items: center;
    line-height: 25px;
  }

  .file-name {
    color: #1890ff;
    cursor: pointer;
    margin-right: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
</style>
