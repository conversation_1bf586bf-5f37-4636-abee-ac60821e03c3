<template>
  <div class="track-replay" style="height: 100%">
    <List
      @itemClick="onItemClick"
      @getList="getList"
      :dataSource="dataSource"
      @getTrackDetail="getTrackDetail"
    />

    <div class="map-content">
      <Map ref="mapRef" :dataSource="dataSource.data" :rowInfo="rowInfo" />
    </div>
  </div>
</template>

<script lang="jsx">
  import List from './components/list'
  import Map from './components/map'
  import { getPatrolTaskList, getTaskTrack } from './services'

  const taskTypeOptions = [
    { label: '全部', value: 99 },
    { label: '计划任务', value: 0 },
    { label: '临时任务', value: 1 },
  ]

  export default {
    name: 'TrackReplay',
    components: { List, Map },
    props: {
      projectId: {},
    },
    data() {
      return {
        dataSource: [],
        rowInfo: null,
      }
    },
    computed: {},
    watch: {},
    created() {},
    methods: {
      onItemClick(item) {
        if (item.taskId == this.rowInfo?.taskId) {
          this.rowInfo = null
        } else {
          this.rowInfo = item
        }
      },
      getList(params) {
        getPatrolTaskList({
          ...params,
          projectId: this.projectId,
          patrolType: '2',
        }).then(res => {
          this.rowInfo = null
          this.dataSource = {
            ...res?.data,
            data: (res.data?.data || [])?.map(el => ({
              ...el,
              lineRange: el.lineRange && JSON.parse(el.lineRange),
            })),
          }
        })
      },
      getTrackDetail(item) {
        getTaskTrack({ taskId: item.taskId }).then(res => {
          let arr = (res?.data || [])?.map(el => [+el.longitude, +el.latitude])

          const trackData = {
            type: 'FeatureCollection',
            features: [
              {
                type: 'Feature',
                properties: {},
                geometry: {
                  coordinates: arr,
                  type: 'LineString',
                },
              },
            ],
          }

          this.$refs.mapRef.onTrackReplay(trackData)
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .track-replay {
    margin: 0 10px;
    display: flex;

    .map-content {
      flex: 1;
      margin-left: 10px;
      border-radius: 2px;
      overflow: hidden;
    }
  }
</style>
