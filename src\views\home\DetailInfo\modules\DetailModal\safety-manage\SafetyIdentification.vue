<template>
  <div style="height: 100%">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="鉴定名称">
        <a-input v-model="queryParam.safetyName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>

      <a-form-item label="工程等级">
        <a-select
          allowClear
          v-model="queryParam.projectLevel"
          placeholder="请选择"
          :options="projectLevelOptions"
        ></a-select>
      </a-form-item>

      <a-form-item label="状态">
        <a-select
          allowClear
          v-model="queryParam.safetyStatus"
          placeholder="请选择"
          :options="safetyStatusOptions"
        ></a-select>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          @refresh="getList"
          @selectChange="selectChange"
          @sortChange="sortChange"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button>
          </div>
        </VxeTable>

        <SafetyIdentificationDetailModal
          v-if="showDetailModal"
          :projectLevelOptions="projectLevelOptions"
          :safetyStatusOptions="safetyStatusOptions"
          :resultsApplicationOptions="resultsApplicationOptions"
          ref="detailModalRef"
          @close="showDetailModal = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import moment from 'moment'
  import { getSafetyPage, deleteSafety } from './services'
  import { getOptions, getProjectTree } from '@/api/common'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import SafetyIdentificationDetailModal from './modules/SafetyIdentificationDetailModal.vue'

  export default {
    name: 'SafetyIdentification',
    components: {
      VxeTable,
      VxeTableForm,
      SafetyIdentificationDetailModal
    },
    props: {
      projectId: {}
    },
    data() {
      return {
        showFormModal: false,
        showDetailModal: false,
        projectLevelOptions: [],
        safetyStatusOptions: [],
        resultsApplicationOptions: [],
        projectOptions: [],

        list: [],
        tableTitle: '安全鉴定',
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,

        queryParam: {
          safetyName: '',
          projectLevel: undefined,
          safetyStatus: undefined,
          projectId: this.projectId,
          pageNum: 1,
          pageSize: 10,
          sort: []
        },
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '鉴定名称',
            field: 'safetyName',
            minWidth: 140
          },
          {
            title: '鉴定单位',
            field: 'safetyUnit',
            minWidth: 140
          },
          {
            title: '鉴定开始时间',
            field: 'startTime',
            minWidth: 140,
            sortable: true
          },
          {
            title: '工程等级',
            field: 'projectLevel',
            minWidth: 120,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.projectLevelOptions.find(el => el.value == row.projectLevel)?.label
              }
            }
          },
          {
            title: '成果应用',
            field: 'resultsApplication',
            minWidth: 120,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.resultsApplicationOptions.find(el => el.value == row.resultsApplication)?.label
              }
            }
          },
          {
            title: '状态',
            field: 'safetyStatus',
            minWidth: 120,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <div class='common-status-box'>
                    <i
                      class={[
                        'common-status-icon',
                        row.safetyStatus == '4' ? 'common-status-completed' : 'common-status-waiting',
                      ]}
                    ></i>
                    <span>{this.safetyStatusOptions.find(el => el.value == row.safetyStatus)?.label}</span>
                  </div>
                )
              },
            },
          },
          {
            title: '结束时间',
            field: 'endTime',
            minWidth: 140,
            sortable: true
          },
          {
            title: '所属工程',
            field: 'projectName',
            minWidth: 140
          },
          {
            title: '操作',
            field: 'operate',
            width: 140,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetail(row)}>查看</a>
                  </span>
                )
              }
            }
          }
        ]
      }
    },
    computed: {},
    watch: {},
    created() {
      getOptions('projectLevel').then(res => {
        this.projectLevelOptions = res.data.map(el => ({ label: el.value, value: el.key }))
      })
      getOptions('safetyStatus').then(res => {
        this.safetyStatusOptions = res.data.map(el => ({ label: el.value, value: el.key }))
      })

      getOptions('resultsApplication').then(res => {
        this.resultsApplicationOptions = res.data.map(el => ({ label: el.value, value: el.key }))
      })

      // 获取工程树
      getProjectTree({}).then(res => {
        this.projectOptions = res.data
      })

      this.getList()
    },
    methods: {
      /** 查询列表 */
      getList() {
        this.showFormModal = false
        this.loading = true
        this.selectChange({ records: [] })
        getSafetyPage(this.queryParam).then(response => {
          this.list = response.data.data
          this.total = response.data.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          pageNum: 1,
          pageSize: 10,
          projectId: this.projectId,
          projectLevel: undefined,
          safetyName: '',
          safetyStatus: undefined,
          sort: []
        }
        this.handleQuery()
      },
      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.safetyId)
        this.names = valObj.records.map(item => item.safetyName)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },
      // 详情
      handleDetail(row) {
        this.showDetailModal = true
        this.$nextTick(() => this.$refs.detailModalRef.handleDetail(row))
      },

      // 导出
      handleExport() {},

      // 操作完成后
      onOperationComplete() {
        this.getList()
      }
    }
  }
</script>
<style lang="less" scoped></style>
