<template>
  <div class="tab-table-panel">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="年份">
        <a-date-picker
          mode="year"
          format="YYYY"
          v-model="queryParam.year"
          placeholder="请选择"
          allow-clear
          style="width: 240px"
          :open="yearShowOne"
          @openChange="openChangeOne"
          @panelChange="panelChangeOne"
          @keyup.enter.native="handleQuery"
        ></a-date-picker>
      </a-form-item>

      <a-form-item label="响应等级">
        <a-select
          show-search
          placeholder="请输入"
          v-model="queryParam.levelResponse"
          option-filter-prop="children"
          :filter-option="filterOption"
          @change="handleChange"
        >
          <a-select-option v-for="item in warningLevelOptions" :key="item.key" :value="item.key">
            {{ item.value }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <!-- <a-form-item label="立卷时间">
        <a-range-picker
          allow-clear
          :value="buildTimes"
          format="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          @change="onRangeChange"
        />
      </a-form-item> -->

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button"></div>
        </VxeTable>

        <FormDetails
          v-if="showFormDetails"
          ref="formDetailsRef"
          :warningLevelOptions="warningLevelOptions"
          :projectOptions="projectOptions"
          @ok="onOperationComplete"
          @close="showFormDetails = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getOptions, getProjectTree } from '@/api/common'
  import { getWarningPage, deleteWarning } from './services'
  import FormDetails from './modules/SafetyDetail.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import getFlatTreeMap from '@/utils/getMapFlatTree'

  export default {
    name: 'EmergencyWarning',
    components: {
      VxeTable,
      VxeTableForm,
      FormDetails
    },
    data() {
      return {
        unitList: [],
        unitArr: [],
        isChecked: false,
        warningLevelOptions: [],
        warningLevels: [],
        projects: [],
        projectOptions: [],
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        showForm: false,
        showFormDetails: false,
        buildTimes: [],
        archivesOptions: [],

        list: [],
        tableTitle: '安全应急预警',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          levelResponse: null,
          pageNum: 1,
          pageSize: 10,
          sort: [],
          year: null
        },
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '年份',
            field: 'year',
            minWidth: 90
          },
          {
            title: '应急启动时间',
            field: 'startTime',
            minWidth: 100
          },
          {
            title: '响应等级',
            field: 'levelResponse',
            minWidth: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.warningLevels[row.levelResponse]?.value || ''
              }
            }
          },
          {
            title: '应急事件说明',
            field: 'eventDescription',
            minWidth: 140,
            showOverflow: 'tooltip'
          },
          {
            title: '结束时间',
            field: 'endTime',
            minWidth: 100
          },
          {
            title: '所属工程',
            field: 'projectId',
            minWidth: 120,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.projects[row.projectId]?.projectName || ''
              }
            }
          },
          {
            title: '操作',
            field: 'operate',
            width: 80,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetails(row)}>查看</a>
                  </span>
                )
              }
            }
          }
        ]
      }
    },
    created() {
      this.getList()
      getOptions('levelResponse').then(res => {
        this.warningLevelOptions = res.data
        this.warningLevels = getFlatTreeMap(this.warningLevelOptions, 'key')
      })

      // 获取工程树
      getProjectTree({ objectCategoryId: undefined }).then(res => {
        this.projectOptions = res.data
        this.projects = getFlatTreeMap(this.projectOptions, 'id')
      })
    },
    mounted() {
      // this.unitList = [{ unitId: 1, unitName: '测试单位1' }]
      // this.unitArr = getFlatTreeMap(this.unitList, 'key')
      // getUnitList({}).then(res => {
      //   if (res?.code == 200) {
      //     this.unitList = res?.data
      //   }
      // })
    },
    methods: {
      // 弹出日历和关闭日历的回调
      openChangeOne(status) {
        if (status) {
          this.yearShowOne = true
        }
      },
      // 得到年份选择器的值
      panelChangeOne(value) {
        this.queryParam.year = moment(value).format('YYYY')
        this.yearShowOne = false
      },
      onRangeChange(value, buildTimes) {
        this.buildTimes = value
        if (buildTimes.length == 0) {
          return
        }
        this.queryParam.startTime = buildTimes[0] ? moment(buildTimes[0]).format('YYYY-MM-DD') + ' 00:00:00' : undefined
        this.queryParam.endTime = buildTimes[1] ? moment(buildTimes[1]).format('YYYY-MM-DD') + ' 23:59:59' : undefined
      },
      /** 查询列表 */
      getList() {
        this.showForm = false
        this.showFormDetails = false
        this.loading = true
        this.selectChange({ records: [] })
        getWarningPage({ ...this.queryParam, projectId: this.$attrs.projectId }).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      //
      handleChange(value) {
        this.queryParam.unitId = value
      },
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.safetyWarningId)
        this.names = valObj.records.map(item => item.safetyWarningName)
        this.isChecked = !!valObj.records.length
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          levelResponse: null,
          pageNum: 1,
          pageSize: 10,
          sort: [],
          year: null
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      }, // 导出

      /* 查看 */
      handleDetails(record) {
        this.showFormDetails = true
        this.$nextTick(() => this.$refs.formDetailsRef.details(record))
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      }
    }
  }
</script>
<style lang="less" scoped>
  .tab-table-panel {
    height: 100%;
  }
</style>
