interface ImportMeta {
  readonly env: ImportMetaEnv
}

export declare global {
  interface ImportMeta {
    env: Record<string, unknown>
  }
  interface Window {}
}

/// <reference types="vite/client" />
//声明.vue文件
declare module '*.vue' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<object, object, any>
  export default component
}
declare module '*'

interface Window {
  // $loadingBar?: import('naive-ui').LoadingBarProviderInst
  // $dialog?: import('naive-ui').DialogProviderInst
  // $message?: import('naive-ui').MessageProviderInst
  // $notification?: import('naive-ui').NotificationProviderInst
}

// declare namespace NaiveUI {
//   type ThemeColor = 'default' | 'error' | 'primary' | 'info' | 'success' | 'warning'
// }
