<template>
  <div style="height: 100%">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="类型">
        <a-select
          allowClear
          v-model="queryParam.filingType"
          placeholder="请选择"
          :options="filingTypeOptions"
        ></a-select>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          @refresh="getList"
          @selectChange="selectChange"
          @sortChange="sortChange"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button>
          </div>
        </VxeTable>
        <RegisterFilingDetailModal
          v-if="showDetailModal"
          :filingTypeOptions="filingTypeOptions"
          ref="detailModalRef"
          @close="showDetailModal = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import moment from 'moment'
  import { getFilingPage, deleteFiling } from './services'
  import { getOptions, getProjectTree } from '@/api/common'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import RegisterFilingDetailModal from './modules/RegisterFilingDetailModal.vue'

  export default {
    name: 'RegisterFiling',
    components: {
      VxeTable,
      VxeTableForm,
      RegisterFilingDetailModal
    },
    props: {
      projectId: {}
    },
    data() {
      return {
        showFormModal: false,
        showDetailModal: false,
        filingTypeOptions: [],
        projectOptions: [],

        list: [],
        tableTitle: '注册备案',
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,

        queryParam: {
          filingType: undefined,
          projectId: this.projectId,
          pageNum: 1,
          pageSize: 10,
          sort: []
        },
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号',  width: 50 },
          {
            title: '时间',
            field: 'enableTime',
            minWidth: 140,
            sortable: true
          },
          {
            title: '类型',
            field: 'filingType',
            minWidth: 120,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.filingTypeOptions.find(el => el.value == row.filingType)?.label
              }
            }
          },
          {
            title: '管理单位',
            field: 'unitManagement',
            minWidth: 120
          },
          {
            title: '单位负责人',
            field: 'headUnit',
            minWidth: 120
          },
          {
            title: '所属工程',
            field: 'projectName',
            minWidth: 100
          },
          {
            title: '操作',
            field: 'operate',
            width: 140,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetail(row)}>查看</a>
                  </span>
                )
              }
            }
          }
        ]
      }
    },
    computed: {},
    watch: {},
    created() {
      getOptions('filingType').then(res => {
        this.filingTypeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
      })

      // 获取工程树
      getProjectTree({ objectCategoryId: this.queryParam.treeNodeId }).then(res => {
        this.projectOptions = res.data
      })

      this.getList()
    },
    methods: {
      /** 查询列表 */
      getList() {
        this.showFormModal = false
        this.loading = true
        this.selectChange({ records: [] })
        getFilingPage(this.queryParam).then(response => {
          this.list = response.data.data
          this.total = response.data.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          filingType: undefined,
          projectId: this.projectId,
          pageNum: 1,
          pageSize: 10,
          sort: []
        }
        this.handleQuery()
      },
      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.filingId)
        this.names = valObj.records.map(item => item.filingName)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },
      // 查看
      handleDetail(row) {
        this.showDetailModal = true
        this.$nextTick(() => this.$refs.detailModalRef.handleDetail(row))
      },

      // 导出
      handleExport() {},

      // 操作完成后
      onOperationComplete() {
        this.getList()
      }
    }
  }
</script>
<style lang="scss" scoped></style>
