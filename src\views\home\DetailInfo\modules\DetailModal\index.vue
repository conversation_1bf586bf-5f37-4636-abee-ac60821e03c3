<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :modalWidth="windowSize.width"
    :modalHeight="windowSize.height"
    @cancel="cancel"
    @onFullScreen="onFullScreen"
    :footer="null"
    ref="detailModalRef"
  >
    <div slot="content" layout="vertical" class="detail-content">
      <div class="detail-tab">
        <a-tabs v-model="tabVal" type="line" :tab-position="'left'" :style="{ height: '100%' }" @change="onTabChange">
          <a-tab-pane
            v-for="(ele, i) in displayInfoOptions"
            :key="ele.key"
            :tab="projectInfo.includes(ele.key) ? ele.option1 : ele.value"
          >
            <!-- 圩区 -->
            <Polder v-if="ele.key === 'projectInfoPolder'" :displayCode="ele.key" :projectId="recordInfo.projectId" />

            <!-- 水闸 -->
            <Sluice v-if="ele.key === 'projectInfoSluice'" :displayCode="ele.key" :projectId="recordInfo.projectId" />

            <!-- 泵站 -->
            <Pump v-if="ele.key === 'projectInfoPump'" :displayCode="ele.key" :projectId="recordInfo.projectId" />

            <!-- 闸站 -->
            <Gate v-if="ele.key === 'projectInfoGate'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 堤防 -->
            <Embankment
              v-if="ele.key === 'projectInfoHP017'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
            />
            <!-- 水电�?-->
            <Hyst v-if="ele.key === 'projectInfoHP003'" :displayCode="ele.key" :projectId="recordInfo.projectId" />

            <!-- 水库大坝 -->
            <Dam v-if="ele.key === 'projectInfoHP002'" :displayCode="ele.key" :projectId="recordInfo.projectId" />

            <!-- 水库 -->
            <Res v-if="ele.key === 'projectInfoHP001'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 灌区 -->
            <Irr v-if="ele.key === 'projectInfoHP004'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- �?�?�?-->
            <Chan v-if="ele.key === 'projectInfoHP005'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 取水�?->
            <Well v-if="ele.key === 'projectInfoHP006'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 农村供水-->
            <Cws v-if="ele.key === 'projectInfoHP013'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 塘坝-->
            <Pond v-if="ele.key === 'projectInfoHP015'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 蓄滞洪区-->
            <Fsda v-if="ele.key === 'projectInfoHP016'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 引调�?->
            <Wadl v-if="ele.key === 'projectInfoHP012'" :displayCode="ele.key" :projectId="recordInfo.projectId" />

            <!-- 淤地�?->
            <Sd v-if="ele.key === 'projectInfoHP020'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 渡槽-->
            <Flum v-if="ele.key === 'projectInfoHP008'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 倒虹�?->
            <Insi v-if="ele.key === 'projectInfoHP009'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 涵洞-->
            <Culv v-if="ele.key === 'projectInfoHP011'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 治河工程-->
            <Grpj v-if="ele.key === 'projectInfoHP019'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 窖池-->
            <Pit v-if="ele.key === 'projectInfoHP014'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 橡胶�?->
            <Ruda v-if="ele.key === 'projectInfoHP021'" :displayCode="ele.key" :projectId="recordInfo.projectId" />

            <!-- 跌水 -->
            <Drop v-if="ele.key === 'projectInfoHP048'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 陡坡 -->
            <Steep v-if="ele.key === 'projectInfoHP049'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 斗门 -->
            <Door v-if="ele.key === 'projectInfoHP050'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 山塘 -->
            <Pool v-if="ele.key === 'projectInfoHP040'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 渠首 -->
            <Canal v-if="ele.key === 'projectInfoHP051'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 隧洞 -->
            <Tunnel v-if="ele.key === 'projectInfoHP023'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 骨干工程 闸站 -->
            <Cprj v-if="ele.key === 'projectInfoHP000'" :displayCode="ele.key" :projectId="recordInfo.projectId" />

            <!-- 划界确权 -->
            <Demarcation
              ref="demarcationRef"
              v-if="ele.key === 'demarcation'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
            />

            <!-- 工程文件预览 -->
            <FileViewList
              v-if="
                ele.key === 'appearance' ||
                ele.key === 'registration' ||
                ele.key === 'safetyAppraisal' ||
                ele.key === 'label'
              "
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
            />

            <!-- 管理单位 -->
            <ManageUnit
              v-if="ele.key === 'unitManagement'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :projectName="modalTitle"
            />

            <!-- 应急管�?-->
            <EmergencyManage
              v-if="ele.key === 'emergencyResponse'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
            />

            <!-- 安全管理 -->
            <SafetyManage
              v-if="ele.key === 'safetyInspection'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
            />

            <!-- 工程检�?-->
            <EngineInspection
              v-if="ele.key === 'inspectionEngineering'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
            />

            <!-- 控制运行 -->
            <Scheduling
              v-if="ele.key === 'controlOperation'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
            />

            <!-- 维修养护 -->
            <MaintenanceUpkeep
              v-if="ele.key === 'maintenance'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
            />

            <!--维养记录-->
            <MaintenanceRecords
              v-if="ele.key === 'maintenanceRecords'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
            />
            <!--应急抢�-->
            <EmergencyRepair
              v-if="ele.key === 'emergencyRepair'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
            />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </ant-modal>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import Demarcation from './Demarcation.vue'
  import FileViewList from './FileViewList.vue'

  import Polder from './polder/index.vue' //圩垸 
  import Sluice from './sluice/index.vue' //水闸 
  import Pump from './pump/index.vue' //泵站
  import Gate from './gate/index.vue' //闸站
  import Embankment from './embankment/index.vue' // 堤防
  import Hyst from './hyst/index.vue' // 水电�?
  import Dam from './dam/index.vue' // 水库大坝
  import Res from './res/index.vue' // 水库
  import Irr from './irr/index.vue' // 灌区
  import Chan from './chan/index.vue' // �?�?�?
  import Well from './well/index.vue' //取水�? 
  import Cws from './cws/index.vue' //农村供水
  import Pond from './pond/index.vue' //塘坝
  import Fsda from './fsda/index.vue' //蓄滞洪区
  import Wadl from './wadl/index.vue' //引调�?

  import Sd from './sd/index.vue' // 淤地�?
  import Flum from './flum/index.vue' // 渡槽
  import Insi from './insi/index.vue' // 倒虹�?
  import Culv from './culv/index.vue' // 涵洞
  import Grpj from './grpj/index.vue' // 治河工程
  import Pit from './pit/index.vue' // 窖池
  import Ruda from './ruda/index.vue' //橡胶�?

  import Cprj from './cprj/index.vue' //骨干工程 闸站
  import Drop from './drop/index.vue' //跌水	drop
  import Steep from './steep/index.vue' //陡坡	steep
  import Door from './door/index.vue' //斗门	door
  import Pool from './pool/index.vue' //山塘	pool
  import Canal from './canal/index.vue' //渠首	canal
  import Tunnel from './tunnel/index.vue' //隧洞	tunnel

  import MaintenanceRecords from './MaintenanceRecords.vue'
  import EmergencyRepair from './EmergencyRepair.vue'

  import ManageUnit from './manageUnit/index.vue'
  import EmergencyManage from './emergency-manage/index.vue'
  import SafetyManage from './safety-manage/index.vue'
  import EngineInspection from './engine-inspection/index.vue'
  import Scheduling from './scheduling/index.vue'
  import MaintenanceUpkeep from './maintenance-upkeep/index.vue'

  import { getDisplayCodes } from '../../services'
  import { getOptions } from '@/api/common'

  export default {
    name: 'CreateForm',
    props: {},
    components: {
      AntModal,
      Demarcation,
      FileViewList,

      Polder,
      Sluice,
      Pump,
      Gate,
      Embankment,
      Hyst,
      Dam,
      Res,
      Irr,
      Chan,
      Well,
      Cws,
      Pond,
      Fsda,
      Wadl,

      Sd,
      Flum,
      Insi,
      Culv,
      Grpj,
      Pit,
      Ruda,

      Cprj,
      Drop,
      Steep,
      Door,
      Pool,
      Canal,
      Tunnel,

      MaintenanceRecords,
      EmergencyRepair,

      ManageUnit,
      EmergencyManage,
      SafetyManage,
      EngineInspection,
      Scheduling,
      MaintenanceUpkeep,
    },
    data() {
      return {
        open: false,
        modalTitle: '',
        tabVal: '',
        windowSize: {},
        displayInfoOptions: [],
        recordInfo: {},
        projectInfo: [
          'projectInfoPolder',
          'projectInfoPump',
          'projectInfoSluice',
          'projectInfoGate',
          'projectInfoHP017',
          'projectInfoHP003',
          'projectInfoHP002',
          'projectInfoHP004',
          'projectInfoHP005',
          'projectInfoHP001',
          'projectInfoHP006',
          'projectInfoHP012',
          'projectInfoHP013',
          'projectInfoHP016',
          'projectInfoHP015',
          'projectInfoHP008',
          'projectInfoHP009',
          'projectInfoHP011',
          'projectInfoHP014',
          'projectInfoHP019',
          'projectInfoHP020',
          'projectInfoHP021',
          'projectInfoHP048', //跌水
          'projectInfoHP049', //陡坡
          'projectInfoHP050', //斗门
          'projectInfoHP040', //山塘
          'projectInfoHP051', //渠首
          'projectInfoHP023', //隧洞
          'projectInfoHP000', //骨干工程 闸站
          'unitManagement',
          'emergencyResponse',
          'safetyInspection',
          'inspectionEngineering',
          'controlOperation',
          'maintenance',
          'demarcation',
        ], // 显示信息key集合
      }
    },
    created() {
      this.windowSize = {
        width: `${parseInt(window.innerWidth * 0.9)}`,
        height: `${parseInt(window.innerHeight * 0.95)}`,
      }
    },
    computed: {},
    watch: {},
    methods: {
      onTabChange(val) {
        this.tabVal = val
      },

      /** 打开 */
      handleDetail(record) {
        this.modalTitle = record.projectName
        this.recordInfo = record

        getDisplayCodes({ projectId: record?.projectId }).then(resp => {
          if (!resp.data?.length) {
            this.$message.info('无展示信息')
            return
          }
          this.open = true
          getOptions('displayInfoTypes').then(res => {
            this.displayInfoOptions = [...this.displayInfoOptions, ...res.data.filter(el => resp.data.includes(el.key))]

            this.tabVal = this.displayInfoOptions[0].key
          })
        })
      },

      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      onFullScreen() {
        this.$refs.demarcationRef?.[0].onFullScreen()
      },
    },
  }
</script>

<style lang="scss" scoped>
  ::v-deep .ant-modal-body {
    padding: 10px;
    background-color: #eef0f3 !important;
    border-radius: 0 0 4px 4px;
    .modal-content {
      width: 100%; 
      height: 100%;
    }
  }

  .detail-content {
    width: 100%;
    height: 100%;
    position: relative;

    .detail-tab {
      height: 100%;

      ::v-deep .ant-tabs-content {
        height: 100%;
        padding-left: 0px;
        border-left: none;
        .ant-tabs-tabpane-active {
          height: 100%;
        }
      }
      ::v-deep .ant-tabs.ant-tabs-left {
        background-color: #fff;
        .ant-tabs-left-bar {
          border-right: 10px solid #eef0f3;
        }
      }
    }
  }
</style>
./polder/index.vue
