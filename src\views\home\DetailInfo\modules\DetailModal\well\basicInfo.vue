<template>
  <div class="basic-info">
    <div class="content">
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        layout="horizontal"
        :labelCol="{ span: 12 }"
        :wrapperCol="{ span: 12 }"
        labelAlign="right"
      >
        <a-row :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">
              基本信息
              <a-button style="margin-left: 10px" type="primary" @click="onBtnClick" :loading="loading">
                {{ this.type == 'detail' ? '编辑' : '确定' }}
              </a-button>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">取水井代码：</span>
              <span class="value">{{ data?.projectCode }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">取水井名称：</span>
              <span class="value" :title="data.projectName">{{ data?.projectName }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="行政区划">
              <div class="form-value">{{ data.districtFullName }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="取水井经度(°)">
              <div class="form-value">{{ data.longitude }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="取水井维度(°)">
              <div class="form-value">{{ data.latitude }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :span="24">
            <a-form-model-item label="取水井所在位置" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <div class="form-value" :title="data.location">{{ data.location }}</div>
            </a-form-model-item>
          </a-col>
          <!-- <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="取水井所在位置">
              <div class="form-value" :title="data.location">{{ data.location }}</div>
            </a-form-model-item>
          </a-col> -->

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title second-title">主要特征信息</div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="井口井管内径规模">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.wellInnDiamLevel"
                placeholder="请选择"
                :options="wellInnDiamLevelOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ wellInnDiamLevelOptions.find(el => el.value == data.wellInnDiamLevel)?.label }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="井深(m)">
              <a-input-number v-if="type == 'edit'" v-model="form.wellDep" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.wellDep }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="井口井管内径(mm)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.wellInnDiam"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.wellInnDiam }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="井壁管材料">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.plpeMat"
                placeholder="请选择"
                :options="plpeMatOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ plpeMatOptions.find(el => el.value == data.plpeMat)?.label }}
              </div>
            </a-form-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="成井年份">
              <a-date-picker
                v-if="type == 'edit'"
                mode="year"
                format="YYYY"
                v-model="form.compYear"
                placeholder="请选择"
                allow-clear
                style="width: 100%"
                :open="yearShowOne"
                @openChange="openChangeOne"
                @panelChange="panelChangeOne"
              ></a-date-picker>
              <div v-else class="form-value">
                {{ data?.compYear }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="设计年取水量(10⁴m³)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.desAnnWain"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.desAnnWain }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="水源类型">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.wasoType"
                placeholder="请选择"
                :options="wasoTypeOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ wasoTypeOptions.find(el => el.value == data.wasoType)?.label }}
              </div>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="备注" prop="note" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <a-textarea v-if="type == 'edit'" class="area-text" v-model="form.note" placeholder="请输入" allowClear />
              <div v-else class="form-value">{{ data.note }}</div>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
  </div>
</template>

<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getWell, updateWell } from './services'
  import moment from 'moment'
  export default {
    name: 'BasicInfo',
    components: {},
    props: {
      projectId: {},
    },
    data() {
      return {
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        loading: false,
        data: {},

        wellInnDiamLevelOptions: [], //井口井管内径规模
        plpeMatOptions: [], //井壁管材料
        wasoTypeOptions: [], //水源类型

        type: 'detail',
        form: {
          compYear: undefined,
          desAnnWain: undefined,
          id: undefined,
          note: '',
          plpeMat: '',
          projectId: undefined,
          wasoType: '',
          wellDep: undefined,
          wellInnDiam: undefined,
          wellInnDiamLevel: '',
        },
        rules: {},
      }
    },
    created() {
      this.init()
      this.getDataSource()
    },
    methods: {
      init() {
        getOptions('wellWasoType').then(res => {
          this.wasoTypeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('plpeMat').then(res => {
          this.plpeMatOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('wellInnDiamLevel').then(res => {
          this.wellInnDiamLevelOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
      },
      // 弹出日历和关闭日历的回调
      openChangeOne(status) {
        if (status) {
          this.yearShowOne = true
        }
      },
      // 得到年份选择器的值
      panelChangeOne(value) {
        this.form.compYear = moment(value).format('YYYY')
        this.yearShowOne = false
      },
      getDataSource() {
        getWell({ projectId: this.projectId }).then(res => {
          this.data = res.data
          this.form = {
            ...res.data,
            projectId: this.projectId,
            compYear: res.data.compYear ? `${res.data.compYear}` : undefined,
            wellInnDiamLevel: res.data.wellInnDiamLevel ? `${res.data.wellInnDiamLevel}` : undefined,
            plpeMat: res.data.plpeMat ? `${res.data.plpeMat}` : undefined,
            wasoType: res.data.wasoType ? `${res.data.wasoType}` : undefined,
          }
        })
      },

      onBtnClick() {
        if (this.type == 'edit') {
          this.form.projectId = this.projectId
          const params = { ...this.form }

          updateWell(params)
            .then(res => {
              this.$message.success('修改成功', 3)
              this.getDataSource()
            })
            .finally(() => (this.loading = false))
        }

        this.type = this.type == 'detail' ? 'edit' : 'detail'
      },
    },
  }
</script>

<style lang="less" scoped>
  @import url('~@/global.less');

  @import url('~@/assets/styles/basic-info.less');
</style>
