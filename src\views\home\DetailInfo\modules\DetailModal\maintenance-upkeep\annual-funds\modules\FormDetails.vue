<template>
  <ant-modal :visible="open" :modal-title="formTitle" modalWidth="600" @cancel="cancel" modalHeight="500">
    <div slot="content">
      <a-form-model ref="form" :model="form">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="24" :md="24" :sm="24">
            <div class="item">
              <label class="common-label-text">年份：</label>
              <span class="common-value-text">{{ yearFormat(form.year) }}</span>
            </div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <div class="item">
              <label class="common-label-text">上级下达的年度维修养护资金(万元)：</label>
              <span class="common-value-text">{{ form.giveCapital }}</span>
            </div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <div class="item">
              <label class="common-label-text">管理单位自筹的年度维修养护资金(万元)：</label>
              <span class="common-value-text">{{ form.raiseCapital }}</span>
            </div>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <div class="item">
              <label class="common-label-text">实际完成的维修养护资金(万元)：</label>
              <span class="common-value-text">{{ form.completionCapital }}</span>
            </div>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <div class="item">
              <label class="common-label-text">已支付的维修养护资金(万元)：</label>
              <span class="common-value-text">{{ form.paidCapital }}</span>
            </div>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <div class="item">
              <label class="common-label-text">所属工程：</label>
              <span class="common-value-text">{{ form.projectName }}</span>
            </div>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getCapitalById } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import moment from 'moment'

  export default {
    name: 'FormDetails',
    props: ['projectOptions'],
    data() {
      return {
        formTitle: '',
        form: {
          capitalId: null,
          completionCapital: null,
          giveCapital: null,
          paidCapital: null,
          projectId: null,
          raiseCapital: null,
          year: null,
        },
        open: false,
        rules: {},
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 年份格式化
      yearFormat(value) {
        if (value) {
          return moment(value).format('YYYY')
        } else {
          return ''
        }
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
          this.planDate = []
          this.taskDate = []
        }
      },

      /** 新增按钮操作 */
      details(row) {
        this.open = true
        if (row != undefined) {
          this.formTitle = '查看'
          getCapitalById({ capitalId: row.capitalId }).then(res => {
            if (res.code == 200) {
              this.form = res.data
              this.form.year = moment(`${res.data.year}-01-01`)
              //附件显示
            }
          })
        }
      },
    },
  }
</script>
<style lang="scss" scoped>
 

  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .file-item {
    display: flex;
    align-items: center;
    line-height: 25px;
  }

  .file-name {
    color: #1890ff;
    cursor: pointer;
    margin-right: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
</style>
