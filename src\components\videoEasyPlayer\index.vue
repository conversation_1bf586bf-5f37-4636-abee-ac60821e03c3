<script setup name="videoEasyPlayer">
  import { getVideoAddress } from '@/views/home/<USER>'
  import { reactive, ref, onMounted, watch } from 'vue'

  const props = defineProps({
    channel: { default: '' },
    device: { default: '' },
    key: 1,
    aspect: { default: '16:9' },
  })

  const state = reactive({
    url: '',
    isFlullscreen: false,
  })

  const videoContentRef = ref(null)
  const videoRef = ref(null)

  watch(
    () => props.channel,
    (newVal, oldVal) => {
      getData()
    },
  )

  function getData() {
    getVideoAddress({
      channel: props.channel,
      device: props.device,
      protocol: 'FLV',
    }).then(res => {
      if (res.data == '无信号') {
        state.url = null
        return
      }
      state.key = Math.random()
      state.url = res.data
    })
  }
  onMounted(() => {
    getData()
  })
</script>

<template>
  <div class="video-div" :key="state.key" ref="videoContentRef">
    <easy-player ref="videoRef" :video-url="state.url" :aspect="aspect.aspect" live easyStretch reconnection />
  </div>
</template>

<style lang="scss" scoped>
  .video-div {
    width: 100%;
    height: 100%;
  }
  :deep(.vjs-control-bar) {
    display: none !important; // 目前UI隐藏
    display: flex;
    width: 100%;
    font-size: 0.12rem;
    align-items: center;

    .vjs-bitrate-control, // 码率
  .vjs-live-control, // 直播
  .vjs-stretch-control,// 拉伸
  .xxxxxx {
      display: none;
    }
  }
  :deep(.easy-player .video-title) {
    top: 0;
    left: 0;
    width: 100%;
    max-width: none;
    font-size: 0.14rem;
    font-family: AlibabaPuHuiTi-Regular;
    color: #ffffff;
    line-height: 0.2rem;
    text-shadow: 0px 0px 0.06rem rgba(0, 0, 0, 0.3);

    background: linear-gradient(90deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0) 100%);
    box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.3);
    text-align: left;
    padding: 0px 0.08rem;
  }
</style>
