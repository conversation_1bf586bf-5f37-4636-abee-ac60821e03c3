<template>
  <MyModal ref="$modal" to=".monitor-modal-box">
    <div size-full v-if="!!url">
      <iframe :src="url" frameborder="0" class="wh-full"></iframe>
    </div>
  </MyModal>
</template>
<script setup lang="tsx" name="Monitor">
  import { useUserStore } from '@/store'
  import { getValueByKey } from '@/api'

  const attrs = useAttrs()

  const userStore = useUserStore()

  let url = $ref(null)

  const [$modal, okLoading, loading, title] = useModal()

  async function openModal(item) {
    // url = `http://localhost:8000/process-line-detail?objectId=${item.objectId}&objectType=${item.objectType}&token=${userStore.token}&type=1`

    $modal.value?.open({
      title: `${item.objectName}监测信息`,
      loading: true,
      contentStyle: { width: '85vw', height: '85vh' },
      showFooter: false,
      onClose() {},
    })

    getValueByKey('site.index.detail.url').then(res => {
      url = `${res.data}?objectId=${item.objectId}&objectType=${item.objectType}&token=${userStore.token}&type=1`
      loading.value = false
    })
  }
  defineExpose({ openModal })
</script>
<style lang="scss" scoped></style>
