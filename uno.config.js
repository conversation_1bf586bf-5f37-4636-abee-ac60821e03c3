import {
  defineConfig,
  presetUno,
  presetIcons,
  presetMini,
  presetAttributify,
  presetTypography,
  transformerDirectives,
  transformerVariantGroup,
  transformerAttributifyJsx,
} from 'unocss'
// import presetRemToPx from '@unocss/preset-rem-to-px'

// import { FileSystemIconLoader } from '@iconify/utils/lib/loader/node-loaders'
// import { getIcons } from './build/plugins/icons.js'
// const icons = getIcons()

export default defineConfig({
  transformers: [transformerDirectives(), transformerVariantGroup(), transformerAttributifyJsx()],
  presets: [
    presetAttributify(),
    presetTypography(),
    // presetRemToPx({ baseFontSize: 4 }),
    presetUno(),
    presetIcons({
      warn: true,
      prefix: ['i-'],
      extraProperties: {
        display: 'inline-block',
        'vertical-align': 'middle',
      },
      // collections: { me: FileSystemIconLoader('./src/assets/svgs') },
    }),
    presetMini(),
  ],
  // safelist: icons.map(icon => `${icon} ${icon}?mask`.split(' ')).flat(),
  shortcuts: [
    {
      'card-border': 'border border-solid border-light_border',
      'auto-bg': 'bg-white',
      'auto-bg-hover': 'hover:bg-#eaf0f1',
      'auto-bg-highlight': 'bg-#eaf0f1',
      'text-highlight': 'rounded-4 px-8 py-2 auto-bg-highlight',
      'primary-pointer-text': 'c-[var(--primary-color)] cursor-pointer',
    },
    {
      'wh-full': 'w-full h-full',
      'flex-center': 'flex justify-center items-center',
      'flex-center-between': 'flex justify-between items-center',
      'flex-x-center': 'flex justify-center',
      'flex-y-center': 'flex items-center',
      'flex-col': 'flex flex-col',
      'flex-col-center': 'flex-center flex-col',
      'absolute-center': 'absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2',
    },
  ],
  rules: [['card-shadow', { 'box-shadow': '0 1px 2px -2px #00000029, 0 3px 6px #0000001f, 0 5px 12px 4px #00000017' }]],
  theme: {
    colors: {
      primary: 'var(--primary-color)',
      primary_hover: 'var(--primary-color-hover)',
      primary_pressed: 'var(--primary-color-pressed)',
      primary_active: 'var(--primary-color-active)',
      info: 'var(--info-color)',
      info_hover: 'var(--info-color-hover)',
      info_pressed: 'var(--info-color-pressed)',
      info_active: 'var(--info-color-active)',
      success: 'var(--success-color)',
      success_hover: 'var(--success-color-hover)',
      success_pressed: 'var(--success-color-pressed)',
      success_active: 'var(--success-color-active)',
      warning: 'var(--warning-color)',
      warning_hover: 'var(--warning-color-hover)',
      warning_pressed: 'var(--warning-color-pressed)',
      warning_active: 'var(--warning-color-active)',
      error: 'var(--error-color)',
      error_hover: 'var(--error-color-hover)',
      error_pressed: 'var(--error-color-pressed)',
      error_active: 'var(--error-color-active)',

      text_main: 'var(--text-main)',
      text_md: 'var(--text-md)',
      text_gray: 'var(--text-gray)',

      light_border: '#efeff5',
    },
  },
})
