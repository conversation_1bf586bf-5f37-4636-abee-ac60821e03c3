<template>
  <div class="action-columns Selected-items" ref="root">
    <a-popover
      v-model="visible"
      placement="bottomRight"
      trigger="click"
      :arrowPointAtCenter="true"
      @click="doParentSetHeight()"
      :get-popup-container="() => $refs.root"
    >
      <div slot="title">
        <a-checkbox :indeterminate="indeterminateAll" :checked="checkAll" @change="onCheckAllChange" class="check-all">
          列展示
        </a-checkbox>

        <a-checkbox
          v-if="columns.some(el => el.type == 'seq')"
          v-model="columns.find(el => el.type == 'seq').visible"
          @change="onCheckOptionChange"
          class="check-all"
        >
          序号列
        </a-checkbox>

        <a-checkbox
          v-if="columns.some(el => el.type == 'checkbox')"
          v-model="columns.find(el => el.type == 'checkbox').visible"
          @change="onCheckOptionChange"
          class="check-all"
        >
          勾选列
        </a-checkbox>

        <a-button @click="resetColumns" type="link">重置</a-button>
      </div>
      <div :style="{ maxHeight: columnHeight + 'px' }" style="overflow: auto" slot="content">
        <div ref="editTable">
          <template v-for="(col, i) in columns || []">
            <div :key="i" v-show="getIsShow(col)" class="list-item">
              <div>
                <SvgIcon style="margin-right: 3px" iconClass="DragColumn" />
                <a-checkbox v-model="col.visible" @change="e => onCheckChange(e, col)" />
                <template v-if="col.title">
                  {{ col.title }}
                </template>
                <slot v-else-if="col.slots && col.slots.title" :name="col.slots.title"></slot>
              </div>

              <div class="fixed-box">
                <a-tooltip title="固定到左侧">
                  <a-icon
                    :class="['fixed-item', col.fixed == 'left' ? 'fixed' : '']"
                    type="vertical-right"
                    @click.stop="
                      e => {
                        e.preventDefault()
                        e.stopPropagation()
                        onFixed(e, col, 'left')
                      }
                    "
                  />
                </a-tooltip>
                <a-divider type="vertical" />
                <a-tooltip title="固定到右侧">
                  <a-icon
                    :class="['fixed-item', col.fixed == 'right' ? 'fixed' : '']"
                    type="vertical-left"
                    @click.stop="
                      e => {
                        e.preventDefault()
                        e.stopPropagation()
                        onFixed(e, col, 'right')
                      }
                    "
                  />
                </a-tooltip>
              </div>
            </div>
          </template>
        </div>
      </div>
      <a-tooltip title="列配置">
        <a-icon class="action" type="setting" />
      </a-tooltip>
    </a-popover>
  </div>
</template>
<script lang="jsx">
  import Sortable from 'sortablejs' // 列交换第三方插件
  import cloneDeep from 'lodash.clonedeep'

  export default {
    name: 'ActionColumns',
    props: {
      myColumns: { type: Array, default: () => [] },
      originColumns: { type: Array, default: () => [] },
    },
    components: {},
    data() {
      return {
        visible: false,
        indeterminateAll: false,
        checkAll: true,
        columnHeight: 50,
        sortable: undefined,
        checkedCounts: this.myColumns.length,
        columns: this.myColumns,
        backColumns: this.originColumns,
      }
    },
    watch: {
      checkedCounts(val) {
        this.checkAll = val === this.columns.length
        this.indeterminateAll = val > 0 && val < this.columns.length
      },
      myColumns: {
        handler(newVal, oldVal) {
          this.columns = newVal
          if (newVal !== oldVal) {
            this.checkedCounts = newVal.length
            this.formatColumns(newVal)
          }
        },
        deep: true,
        immediate: true,
      },
      originColumns: {
        handler(newVal, oldVal) {
          this.backColumns = newVal
        },
        deep: true,
        immediate: true,
      },
    },
    created() {
      this.formatColumns(this.columns)
    },
    mounted() {},
    methods: {
      doParentSetHeight() {
        this.$emit('setColumnHeight')
        this.rowDrop()
      },
      setHeight(height) {
        let columnsHeight = height - 160
        if (columnsHeight < 0) {
          columnsHeight = 35
        }
        this.columnHeight = columnsHeight
      },
      onCheckChange(e, col) {
        if (!col.visible) {
          this.checkedCounts -= 1
        } else {
          this.checkedCounts += 1
        }
        this.$emit('changeColumns', this.columns)
      },
      fixColumn(fixed, col) {
        if (fixed !== col.fixed) {
          this.$set(col, 'fixed', fixed)
        } else {
          this.$set(col, 'fixed', undefined)
        }
      },
      setSearch(col) {
        this.$set(col, 'searchAble', !col.searchAble)
        if (!col.searchAble && col.search) {
          this.resetSearch(col)
        }
      },
      resetSearch(col) {
        // col.search.value = col.dataType === 'boolean' ? false : undefined
        col.search.value = undefined
        col.search.backup = undefined
      },
      resetColumns() {
        this.checkedCounts = this.backColumns.length
        this.backColumns.forEach((col, index) => {
          col.visible = true
        })
        this.$forceUpdate()
        this.$emit('changeColumns', this.backColumns)
      },
      onCheckAllChange(e) {
        if (e.target.checked) {
          this.checkedCounts = this.columns.length
          this.columns.forEach(function (col, index) {
            col.visible = true
          })
        } else {
          this.checkedCounts = 0
          this.columns.forEach(function (col, index) {
            col.visible = false
          })
        }
        this.$emit('changeColumns', this.columns || [])
      },
      onCheckOptionChange(e) {
        this.$emit('changeColumns', this.columns || [])
      },
      getConditions(columns) {
        const conditions = {}
        columns
          .filter(item => item.search.value !== undefined && item.search.value !== '' && item.search.value !== null)
          .forEach(col => {
            conditions[col.dataIndex] = col.search.value
          })
        return conditions
      },
      formatColumns(columns) {
        for (const col of columns) {
          if (col.visible === undefined) {
            this.$set(col, 'visible', true)
          }
          if (!col.visible) {
            this.checkedCounts -= 1
          }
        }
      },

      // 固定列
      onFixed(e, col, type) {
        const idx = this.columns.findIndex(el => el.title == col.title)
        const fixed = this.columns[idx].fixed
        if (fixed) {
          if (fixed == type) {
            this.columns[idx].fixed = false
          } else {
            this.columns[idx].fixed = type
          }
        } else {
          this.columns[idx].fixed = type
        }

        this.$forceUpdate()
        this.$emit('changeColumns', this.columns || [])
      },
      getIsShow(col) {
        if (col?.type == 'seq' || col?.type == 'checkbox' || col?.field == 'operate') {
          return false
        }
        return true
      },

      /**
       * 行拖拽事件
       */
      rowDrop() {
        const that = this
        this.$nextTick(() => {
          const xGrid = this.$refs.editTable
          const el = xGrid
          this.sortable = Sortable.create(el, {
            handle: '.list-item',
            animation: 300,
            chosenClass: 'list-item-drag', // 被选中项的css 类名
            dragClass: 'list-item-drag', // 正在被拖拽中的css类名
            onEnd: ({ item, newIndex, oldIndex }) => {
              const dealColumns = cloneDeep(this.columns)
              const currRow = dealColumns.splice(oldIndex, 1)[0]
              dealColumns.splice(newIndex, 0, currRow)
              this.columns = dealColumns

              // that.$emit('rowDrop', that.columns)
              that.$emit('changeColumns', that.columns)
            },
            onUpdate(event) {
              const newIndex = event.newIndex
              const oldIndex = event.oldIndex
              const $body = el
              const $tr = $body.children[newIndex]
              const $oldTr = $body.children[oldIndex]
              // 先删除移动的节点
              $body.removeChild($tr)
              // 再插入移动的节点到原有节点，还原了移动的操作
              if (newIndex > oldIndex) {
                $body.insertBefore($tr, $oldTr)
              } else {
                $body.insertBefore($tr, $oldTr.nextSibling)
              }
            },
          })
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .action-columns {
    display: inline-block;

    .left,
    .right {
      transform: rotate(-90deg);
    }
  }
  /* 表格设置调整表头顺序部分 */
  .Selected-items {
    ::v-deep .ant-popover-title {
      padding: 5px 16px;
      .ant-btn {
        text-align: right;
        min-width: auto;
        color: #1890ff;
        margin-left: 5px;
        font-size: 15px;
        margin-top: 0;
        padding: 0;
      }
    }
    ::v-deep .ant-popover-inner-content {
      padding: 0px;

      .list-item {
        cursor: move;
        padding: 0px 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        line-height: 35px;
        .fixed-box {
          // text-align: right;
          .fixed-item {
            cursor: pointer;
          }

          .fixed {
            color: #1890ff;
          }
        }
        .ant-checkbox-wrapper {
          margin-right: 4px;
        }
      }

      .list-item-drag {
        background: #ffffff;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.15);
        z-index: 9999;
        box-sizing: border-box;
      }
    }
  }
</style>
