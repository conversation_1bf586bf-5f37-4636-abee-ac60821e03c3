<template></template>
<script setup lang="tsx" name="GeoJsonLine">
  import { GeoJsonLayer } from '@deck.gl/layers'
  import { hexToRgb } from '../utils/toDeckglRgb.js'
  import overlayManager from '../utils/overlayManager.js'
  import * as turf from '@turf/turf'

  const attrs: any = useAttrs()
  const activeItem = defineModel('activeItem')
  const lastZoom = ref(attrs.zoom)
  const lastActive = ref(null)

  // 使用全局 overlay 管理器，不再创建独立的 overlay
  const layerId = `line-layer-${attrs.id}`

  const updateProps = () => {
    const json = JSON.parse(JSON.stringify(attrs.geojson))
    // 给每个区域加质点
    const geojson = {
      ...json,
      features: [
        ...json.features.map(el => {
          return turf.centerOfMass({ features: [el], type: 'FeatureCollection' }, { properties: el.properties })
        }),
        ...json.features,
      ],
    }

    // 创建图层
    const layer = new GeoJsonLayer({
          id: 'geojson-layer-line-' + attrs.id,
          data: JSON.parse(JSON.stringify(geojson)),
          filled: true,
          pickable: true,

          stroked: true,
          getLineWidth: 6,
          lineWidthMaxPixels: 10, //线条的最大宽度（以像素为单位）
          lineWidthMinPixels: 2, //线条的最小宽度（以像素为单位）
          getLineColor: d => {
            if (activeItem.value?.id === d.properties.object_id) {
              return [...hexToRgb('#F5DD7E'), 255]
            }
            return [...hexToRgb(d.properties.color), 255]
          },

          pointType: 'text',
          getText: d => {
            if (attrs.zoom <= 13) return ''
            return d.properties?.object_name
          },
          getTextColor: [29, 33, 41, 255],
          textCharacterSet: 'auto',
          getTextSize: 12,
          textOutlineColor: [255, 255, 255, 255],
          textOutlineWidth: 7,
          textFontSettings: { sdf: true, smoothing: 0.3 },

          onClick(opt) {
            const options = JSON.parse(JSON.stringify(opt.object))

            if (activeItem.value?.id === options.properties.object_id) {
              activeItem.value = { gisLayer: activeItem.value.gisLayer }
            } else {
              activeItem.value = {
                tabVal1: attrs.tabLevel1,
                tabVal2: attrs.tabLevel2,
                ...options.properties,
                name: options.properties.object_name,
                id: options.properties.object_id,
                gisLayer: attrs.gisLayer,
              }
            }
          },
          // onHover: opt => {},
        })

    // 使用全局 overlay 管理器添加图层
    overlayManager.addLayer(layerId, layer)
  }

  watch(
    () => attrs.geojson,
    newVal => {
      nextTick(() => {
        updateProps()
      })
    },
  )

  watch(
    () => activeItem.value,
    newVal => {
      // 切换其他图层时，上一次选中的是当前图层
      if (attrs.gisLayer === lastActive.value?.gisLayer) {
        updateProps()
      }
      // 选中和取消当前图层
      if (attrs.gisLayer === newVal?.gisLayer) {
        updateProps()
      }

      nextTick(() => {
        lastActive.value = { ...newVal }
      })
    },
  )

  // 缩放时
  watch(
    () => attrs.zoom,
    newVal => {
      // if (newVal < 8) {
      //   stateRef.value.deckOverlay.setProps({ layers: [] })
      // } else {
      //   // if (stateRef.value.deckOverlay?._props?.layers?.length > 0) return
      // }
      if (lastZoom.value > 13 && newVal <= 13) {
        nextTick(() => updateProps())
      }
      if (lastZoom.value <= 13 && newVal > 13) {
        nextTick(() => updateProps())
      }

      lastZoom.value = newVal
    },
  )

  onMounted(() => {
    nextTick(() => {
      updateProps()
      // 通知父组件图层已挂载，传递 overlay 管理器的引用
      attrs.onMounted && attrs.onMounted(attrs.id, overlayManager)
    })
  })

  onBeforeUnmount(() => {
    // 组件销毁时移除图层
    overlayManager.removeLayer(layerId)
  })
</script>
