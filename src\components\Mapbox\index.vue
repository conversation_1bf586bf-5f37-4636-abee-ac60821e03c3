<template>
  <div ref="mapContainer" class="map-container"></div>
</template>

<script setup name="MapboxDeckGL">
  import { ref, onMounted, onBeforeUnmount } from 'vue'
  import mapboxgl from 'mapbox-gl'
  import 'mapbox-gl/dist/mapbox-gl.css'

  const attrs = useAttrs()

  const mapContainer = ref(null)

  const mapIns = ref(null)

  onMounted(() => {
    nextTick(() => {
      createMap()
    })
  })

  const createMap = () => {
    // 设置Mapbox的访问令牌
    mapboxgl.accessToken =
      attrs.mapboxAccessToken || 'pk.eyJ1IjoiaGhjY2RldiIsImEiOiJjbTBxaDBhdmswYzZjMmpwdzE4eWU2d2NvIn0._XkHfjxUcOLIZ7bIJUcbWw'

    // 初始化Mapbox地图
    mapIns.value = new mapboxgl.Map({
      container: mapContainer.value,
      // style: attrs.mapStyle || 'mapbox://styles/hhccdev/cm0qim4j1002901pqhid47z4z',
      style: {
        version: 8,
        sources: {
          composite: {
            url: 'mapbox://mapbox.mapbox-streets-v8',
            type: 'vector',
          },
        },
        sprite: 'mapbox://sprites/mapbox/streets-v8',
        glyphs: 'mapbox://fonts/mapbox/{fontstack}/{range}.pbf',
        layers: [
          {
            id: 'background',
            type: 'background',
            paint: {
              'background-color': 'transparent',
            },
            interactive: true,
          },
        ],
      },
      center: attrs.center || [121.22727132826859, 29.348006827230503],
      zoom: attrs.mapZoom || 4,
      maxZoom: 17.49, // 天地图大于这个值时，图层会消失
      // minZoom: 5.3,
      pitch: 0, // 相对于地面3D视角的角度
      antialias: false, //抗锯齿，通过false关闭提升性能
      // projection: 'globe', // equalEarth :WGS84投影 	mercator:墨卡托投影(默认)
      // maxBounds: [
      //   [73.66, 3.86],
      //   [135.05, 53.55],
      // ],
      ...attrs?.options,
    })

    // mapIns.value.addControl(new mapboxgl.NavigationControl({ showCompass: false }), 'top-left')
    // 鼠标样式
    mapIns.value.getCanvas().style.cursor = 'auto'

    mapIns.value.once('load', () => {
      attrs?.onMapMounted && attrs?.onMapMounted(mapIns.value)
    })

    mapIns.value.on('style.load', () => {
      attrs?.onMapStyleLoad && attrs?.onMapStyleLoad(mapIns.value)
    })

    mapIns.value.on('zoomend', e => {
      attrs?.onMapZoomEnd && attrs?.onMapZoomEnd(e.target.getZoom(), e)
    })

    mapIns.value.on('dragend', e => {
      attrs?.onMapDragEnd && attrs?.onMapDragEnd(mapIns.value.getBounds())
    })
  }

  onBeforeUnmount(() => {
    if (mapIns.value) {
      mapIns.value.remove() // 销毁地图实例
    }
  })
</script>

<style scoped>
  .map-container {
    height: 100%;
    width: 100%;
  }
</style>
