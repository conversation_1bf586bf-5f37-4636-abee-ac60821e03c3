<template>
  <ant-modal :visible="open" :modal-title="formTitle" modalWidth="600" @cancel="cancel" modalHeight="500">
    <div slot="content">
      <a-form-model ref="form" :model="form">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="24" :md="24" :sm="24">
            <label class="common-label-text">养护内容：</label>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <div class="common-text-item">
              <span class="common-value-text">{{ form.maintenanceContent }}</span>
            </div>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <label class="common-label-text">养护人员：</label>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <div class="common-text-item">
              <span class="common-value-text">{{ form.maintenancePeople }}</span>
            </div>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">处理结果：</label>
              <span class="common-value-text">{{ taskStatusFormat(form.taskStatus) }}</span>
            </div>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">养护时间：</label>
              <span class="common-value-text">{{ form.maintenanceTime }}</span>
            </div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">所属工程：</label>
              <span class="common-value-text">{{ form.projectName }}</span>
            </div>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <div>
              <label class="common-label-text">附件：</label>
              <div
                class="file-item"
                v-for="(el, i) in form.positionAttaches"
                :key="i"
                @click="() => downLoad(el.attachUrl)"
              >
                <a-icon type="paper-clip" />
                <div class="file-name" style="margin-left: 5px">{{ el.attachName }}</div>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getTaskById } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import moment from 'moment'

  export default {
    name: 'FormDetails',
    components: { AntModal },
    props: ['projectOptions', 'taskStatusOptions'],
    data() {
      return {
        formTitle: '',
        form: {
          maintenanceContent: '',
          maintenancePeople: '',
          maintenanceTaskId: null,
          maintenanceTime: '',
          positionAttaches: [],
          projectId: null,
          taskStatus: null,
        },
        open: false,
        rules: {},
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 处理情况格式化
      taskStatusFormat(value) {
        if (value) {
          return this.taskStatusOptions.find(item => item.key == value)?.value
        } else {
          return ''
        }
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
          this.planDate = []
          this.taskDate = []
        }
      },

      /** 新增按钮操作 */
      details(row) {
        this.open = true
        if (row != undefined) {
          this.formTitle = '查看'
          getTaskById({ maintenanceTaskId: row.maintenanceTaskId }).then(res => {
            if (res.code == 200) {
              this.form = res.data
              //附件显示
            }
          })
        }
      },

      downLoad(url) {
        window.open(url)
      },
    },
  }
</script>
<style lang="less" scoped>
  @import url('~@/global.less');

  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .file-item {
    display: flex;
    align-items: center;
    line-height: 25px;
  }

  .file-name {
    color: @primary-color;
    cursor: pointer;
    margin-right: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
</style>
