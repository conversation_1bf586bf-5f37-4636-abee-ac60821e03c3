<template>
  <div class="basic-info">
    <div class="content">
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        layout="horizontal"
        :labelCol="{ span: 12 }"
        :wrapperCol="{ span: 12 }"
        labelAlign="right"
      >
        <a-row :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">
              基本信息
              <a-button style="margin-left: 10px" type="primary" @click="onBtnClick" :loading="loading">
                {{ this.type == 'detail' ? '编辑' : '确定' }}
              </a-button>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">蓄滞洪区代码：</span>
              <span class="value">{{ data?.projectCode }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">蓄滞洪区名称：</span>
              <span class="value">{{ data?.projectName }}</span>
            </div>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="行政区划">
              <div class="form-value">{{ data.districtFullName }}</div>
            </a-form-model-item>
          </a-col>
          <!-- <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="蓄滞洪区所在位置">
              <div class="form-value" :title="data.location">{{ data.location }}</div>
            </a-form-model-item>
          </a-col> -->
          <a-col :span="24">
            <a-form-model-item label="蓄滞洪区所在位置" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <div class="form-value" :title="data.location">{{ data.location }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="左下角经度(°)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.lowLeftLong"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.lowLeftLong }}</div>
              <!-- <div class="form-value">{{ data.longitude }}</div> -->
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="左下角维度(°)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.lowLeftLat"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.lowLeftLat }}</div>
              <!-- <div class="form-value">{{ data.latitude }}</div> -->
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="右上角经度(°)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.upRightLong"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.upRightLong }}</div>
              <!-- <div class="form-value">{{ data.longitude }}</div> -->
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="右上角维度(°)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.upRightLat"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.upRightLat }}</div>
              <!-- <div class="form-value">{{ data.latitude }}</div> -->
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title second-title">主要特征信息</div>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="蓄滞洪区类型">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.fsdaType"
                placeholder="请选择"
                :options="fsdaTypeOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ fsdaTypeOptions.find(el => el.value == data.fsdaType)?.label }}
              </div>
            </a-form-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="设区时间">
              <a-date-picker
                v-if="type == 'edit'"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                v-model="form.buildFlDate"
                style="width: 100%"
              />
              <div v-else class="form-value">{{ data.buildFlDate }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="蓄滞洪区总面积(km²)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.fsdaTotArea"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.fsdaTotArea }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="蓄滞(行)洪区圩堤长度(km)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.fsdaDikeLen"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.fsdaDikeLen }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="设计行(蓄)洪面积(km²)">
              <a-input-number v-if="type == 'edit'" v-model="form.desFlArea" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.desFlArea }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="设计行(蓄)洪水位(m)">
              <a-input-number v-if="type == 'edit'" v-model="form.desFlStag" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.desFlStag }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="设计蓄洪量(10⁴m³)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.desStorCap"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.desStorCap }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="设计行洪流量(m³/s)">
              <a-input-number v-if="type == 'edit'" v-model="form.desFlFlow" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.desFlFlow }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="耕地面积(亩)">
              <a-input-number v-if="type == 'edit'" v-model="form.arArea" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.arArea }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="有效蓄洪容积(10⁴m³)">
              <a-input-number v-if="type == 'edit'" v-model="form.effFlVol" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.effFlVol }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :span="24">
            <a-form-model-item label="备注" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <a-textarea v-if="type == 'edit'" class="area-text" v-model="form.note" placeholder="请输入" allowClear />
              <div v-else class="form-value">{{ data.note }}</div>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
  </div>
</template>

<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getFsda, updateFsda } from './services'
  import { updateListFromStr } from '@/utils/util.js'
  export default {
    name: 'BasicInfo',
    components: {},
    props: {
      projectId: {},
    },
    data() {
      return {
        loading: false,
        data: {},

        fsdaTypeOptions: [], //蓄滞洪区类型

        type: 'detail',
        form: {
          arArea: undefined,
          buildFlDate: '',
          desFlArea: undefined,
          desFlFlow: undefined,
          desFlStag: undefined,
          desStorCap: undefined,
          effFlVol: undefined,
          fsdaDikeLen: undefined,
          fsdaTotArea: undefined,
          fsdaType: '',
          id: undefined,
          lowLeftLat: '',
          lowLeftLong: '',
          note: '',
          projectId: undefined,
          upRightLat: '',
          upRightLong: '',
        },
        rules: {},
      }
    },
    created() {
      this.init()
      this.getDataSource()
    },
    methods: {
      init() {
        getOptions('fsdaType').then(res => {
          this.fsdaTypeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
      },
      getDataSource() {
        getFsda({ projectId: this.projectId }).then(res => {
          this.data = res.data
          this.form = {
            ...res.data,
            projectId: this.projectId,

            // supWasoType: res.data?.supWasoType ? updateListFromStr(res.data?.supWasoType) : undefined,
            fsdaType: res.data.fsdaType ? `${res.data.fsdaType}` : undefined,
          }
        })
      },

      onBtnClick() {
        if (this.type == 'edit') {
          // this.form.supWasoType = this.form.supWasoType ? this.form.supWasoType.join(',') : ''
          this.form.projectId = this.projectId
          const params = { ...this.form }

          updateFsda(params)
            .then(res => {
              this.$message.success('修改成功', 3)
              this.getDataSource()
            })
            .finally(() => (this.loading = false))
        }

        this.type = this.type == 'detail' ? 'edit' : 'detail'
      },
    },
  }
</script>

<style lang="scss" scoped>
 

  @import url('~@/assets/styles/basic-info.scss');
</style>
