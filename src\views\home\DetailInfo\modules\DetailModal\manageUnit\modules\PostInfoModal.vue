<template>
  <ant-modal :visible="open" :modal-title="modalTitle" modalWidth="700" @cancel="cancel" modalHeight="600">
    <div slot="content">
      <a-tabs v-model="tabKey" size="small">
        <a-tab-pane v-for="(ele, i) in list" :key="`${i + 1}`" :tab="`${ele.positionName}`">
          <a-row :gutter="32">
            <a-col :lg="12" :md="12" :sm="24">
              <div class="item">
                <label class="common-label-text">岗位类别：</label>
                <span class="common-value-text">
                  {{ positionTypeOptions?.find(el => el.key == ele.positionType)?.value }}
                </span>
              </div>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <div class="item">
                <label class="common-label-text">在岗人数(人)：</label>
                <span class="common-value-text">{{ ele.numberOfPersons }}</span>
              </div>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <div class="item">
                <label class="common-label-text">人员兼岗情况：</label>
                <span class="common-value-text">{{ ele.numberOfPersons == 1 ? '是' : '否' }}</span>
              </div>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <div class="item">
                <label class="common-label-text">是否物业化：</label>
                <span class="common-value-text">{{ ele.isMaterialization == 1 ? '是' : '否' }}</span>
              </div>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24">
              <label class="common-label-text">所属工程：</label>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24">
              <div class="common-text-item">
                <span class="common-value-text" :title="ele.projectName">{{ ele.projectName }}</span>
              </div>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <label class="common-label-text">入职条件：</label>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <div class="common-text-item">
                <span class="common-value-text" :title="ele.conditionsOfEntry">{{ ele.conditionsOfEntry }}</span>
              </div>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <label class="common-label-text">岗位职责：</label>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <div class="common-text-item">
                <span class="common-value-text" :title="ele.thePosition">{{ ele.thePosition }}</span>
              </div>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <div>
                <label class="common-label-text">证书附件：</label>
                <div
                  class="file-item"
                  v-for="(el, i) in ele.positionAttaches"
                  :key="i"
                  @click="() => downLoad(el.attachUrl)"
                >
                  <a-icon type="paper-clip" />
                  <div class="file-name" style="margin-left: 5px">{{ el.attachName }}</div>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-tab-pane>
      </a-tabs>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getPositionPage } from '../services'
  import moment from 'moment'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'DetailModal',
    props: ['positionTypeOptions'],
    components: { AntModal },
    data() {
      return {
        open: false,
        modalTitle: '',
        list: [],

        tabKey: '1',
      }
    },
    filters: {},
    created() {},
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      /** 查看按钮操作 */
      handleDetail(record) {
        this.open = true
        this.modalTitle = '岗位信息'

        getPositionPage({
          projectId: record.projectId,
          positionType: record.key,
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
        }).then(res => {
          this.list = res.data?.data || []
        })
      },
      downLoad(url) {
        window.open(url)
      },
    },
  }
</script>

<style lang="scss" scoped>
 

  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .file-item {
    display: flex;
    align-items: center;
    line-height: 25px;
  }

  .file-name {
    color: #1890ff;
    cursor: pointer;
    margin-right: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
</style>
