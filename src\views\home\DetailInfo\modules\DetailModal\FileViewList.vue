<template>
  <div class="file-view-list">
    <div class="top">
      <a-button type="primary" @click="onUploadClick">新增</a-button>
    </div>
    <div class="content">
      <a-empty v-if="list.length === 0" />
      <div v-else class="scroll-box">
        <div class="img-model-box">
          <div v-for="(ele, i) in list || []" :key="i" class="img-model-item">
            <div class="img-box">
              <img :src="ele.attachUrl || img" alt="" />
              <div class="buttons">
                <a-button type="primary" @click="() => onDetailClick(ele)">详情</a-button>
                <a-button @click="() => onDeleteClick(ele)">删除</a-button>
              </div>
            </div>
            <div>{{ ele.attachName }}</div>
          </div>
        </div>
      </div>
    </div>
    <AddFileModal v-if="showAddModal" title="新增" ref="AddFileModalRef" @confirm="onAddFileModalConfirm" />
  </div>
</template>

<script lang="jsx">
  import { getProjectAttachList, addProjectAttach, deleteProjectAttach } from '../../services'
  import AEmpty from 'ant-design-vue/es/empty'
  import AddFileModal from './components/AddFileModal.vue'

  export default {
    name: 'FileViewList',
    props: {},
    components: { AEmpty, AddFileModal },
    data() {
      return {
        showAddModal: false,
        img: require('../../default-file.png'),
        list: []
      }
    },
    computed: {},
    watch: {},
    created() {
      this.getList()
    },
    methods: {
      getList() {
        getProjectAttachList({ displayCode: this.$attrs.displayCode, projectId: this.$attrs.projectId }).then(res => {
          this.list = res.data || []
        })
      },
      onUploadClick() {
        this.showAddModal = true
        this.$nextTick(() => this.$refs.AddFileModalRef.showModal())
      },
      onAddFileModalConfirm(params, callback) {
        this.showAddModal = false
        addProjectAttach({
          attachName: params.name,
          attachUrl: params.url,
          projectId: this.$attrs.projectId,
          displayCode: this.$attrs.displayCode
        }).then(res => {
          callback()
          this.getList()
        })
      },

      onDetailClick() {},
      onDeleteClick(ele) {
        const that = this
        this.$confirm({
          title: `确认删除${ele.attachName}?`,
          onOk() {
            return deleteProjectAttach({ projectAttachId: ele.projectAttachId }).then(() => {
              that.getList()
              that.$message.success('删除成功', 3)
            })
          },
          onCancel() {}
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .file-view-list {
    padding: 10px;
    height: 100%;
    .content {
      height: calc(100% - 50px);
      margin-top: 10px;
    }
    .scroll-box {
      height: calc(100% - 32px);
      overflow-y: auto;
      .img-model-box {
        display: grid;
        justify-content: space-between;
        grid-template-columns: repeat(auto-fill, 180px);
        grid-gap: 20px;
        padding-bottom: 20px;
        .img-model-item {
          height: 210px;
          border-radius: 4px;
          text-align: center;
          font-weight: 600;
          cursor: pointer;
          .img-box {
            width: 100%;
            height: 180px;
            margin-bottom: 5px;
            .buttons {
              width: 100%;
              .ant-btn {
                width: 50%;
              }
            }
            > img {
              border-radius: 4px;
              width: 100%;
              height: 180px;
            }
          }
        }
      }
    }
  }
</style>
