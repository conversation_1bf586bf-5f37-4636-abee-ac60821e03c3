<template>
  <div>
    <a-modal :title="title" :visible="visible" :confirm-loading="confirmLoading" @ok="handleOk" @cancel="handleCancel">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-form-model-item label="附件名称" prop="name">
          <a-input allowClear :disabled="!!readeOnlyName" v-model="form.name" placeholder="请输入" />
        </a-form-model-item>

        <a-form-model-item label="附件">
          <UploadFile :fileUrl.sync="form.url" folderName="projectFile" :multiple="false" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>
<script lang="jsx">
  import UploadFile from '@/components/UploadFile/index.vue'

  export default {
    name: 'AddFileModal',
    components: { UploadFile },
    props: {
      title: {
        type: String,
        default: () => ''
      },
      readeOnlyName: {
        type: String,
        default: () => ''
      }
    },
    data() {
      return {
        visible: false,
        confirmLoading: false,
        form: {
          name: undefined,
          url: undefined
        },
        rules: {
          name: [{ required: true, message: '附件名称不能为空', trigger: 'blur' }]
        }
      }
    },
    watch: {
      readeOnlyName: {
        handler(newVal, oldVal) {
          this.form.name = newVal
        },
        immediate: true
      }
    },
    methods: {
      showModal() {
        this.visible = true
      },
      handleOk() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.confirmLoading = true

            this.$listeners.confirm &&
              this.$listeners.confirm(this.form, () => {
                this.visible = false
                this.confirmLoading = false
              })
          } else {
            return false
          }
        })
      },
      handleCancel(e) {
        this.visible = false
      }
    }
  }
</script>
