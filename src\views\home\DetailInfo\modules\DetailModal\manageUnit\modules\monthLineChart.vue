<template>
  <LineEchart :dataSource="lineChart.dataSource" :custom="lineChart.custom" height="240px" />
</template>

<script lang="jsx">
  import { LineEchart } from '@/components/Echarts'

  export default {
    name: 'MonthLineEchart',
    components: { LineEchart },
    props: ['dataSource'],
    data() {
      return {
        lineChart: {
          dataSource: [],
          custom: {
            shortValue: true,
            dataZoom: false,
            showAreaStyle: false,
          },
        },
      }
    },
    watch: {
      dataSource: {
        handler(newVal, oldVal) {
          this.lineChart.dataSource = [
            {
              name: '次数',
              data: newVal?.map(el => [el.month + '', el.monthTrainingCount]),
            },
          ]
        },
        deep: true,
      },
    },
    created() {},
    methods: {},
  }
</script>

<style lang="scss" scoped></style>
