<template>
  <div class="panel">
    <div class="header bg-#E8F3FF pl-0.16rem select-none h-0.52rem flex-y-center font-[SourceHanSansCN-Medium]">图层控制</div>

    <n-tabs animated size="small" type="line" v-model:value="state.tabLevel1" @update:value="changeTabLevel1">
      <n-tab v-for="el in attrs.tabLevelOptions1" :name="el.objectCategoryId" :tab="el.objectCategoryName"></n-tab>
    </n-tabs>

    <div class="flex-1 p-0.16rem relative overflow-hidden">
      <n-spin v-if="state.loading" size="small" class="absolute-center z-1 size-full bg-[rgba(255,255,255,.6)]" />

      <div class="h-full overflow-auto pr-0.06rem mr-[-0.06rem]">
        <n-collapse>
          <template #arrow>
            <MyIcon name="caret-down" class="text-0.14rem" />
          </template>
          <n-collapse-item
            v-for="(el, idx) in state.dataSource"
            :name="el.objectCategoryCode"
            :class="{ active: attrs.activeTabLevel2?.objectCategoryCode === el?.objectCategoryCode }"
          >
            <template #header>
              <div class="flex-y-center">
                <n-checkbox
                  @click.stop
                  v-model:checked="el.checked"
                  :indeterminate="el.indeterminate"
                  @update:checked="val => handleHeaderChecked(val, el, idx)"
                />
                <div class="size-0.2rem items-center mx-0.08rem">
                  <MyIcon :name="el.objectCategoryCode" class="text-0.18rem" />
                </div>
                {{ el.objectCategoryName }}
              </div>
            </template>
            <template #header-extra>
              <span class="c-text_gray">{{ el.total }}</span>
            </template>
            <div>
              <div
                v-for="(ele, index) in el.items"
                class="leaf pl-0.3rem pr-0.08rem h-0.32rem b-rd-0.04rem mt-0.04rem flex-center-between text-0.14rem"
              >
                <div class="flex-y-center">
                  <n-checkbox
                    @click.stop
                    v-model:checked="ele.checked"
                    @update:checked="val => handleItemChecked(val, el, ele, idx, index)"
                  />

                  <div class="size-0.2rem items-center mx-0.08rem relative">
                    <div
                      class="w-0.18rem h-0.18rem"
                      :style="{ background: `url(${ele.icon}) no-repeat center / 100% 100%` }"
                    ></div>
                  </div>

                  {{ ele.name }}
                </div>
                <span class="c-text_gray">{{ ele.count }}</span>
              </div>
            </div>
          </n-collapse-item>
        </n-collapse>
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx" name="Right">
  import { objectCategoryCountByFirstLevel } from '../services'
  import { colors, getColor, getIcon } from '../config.ts'

  const attrs = useAttrs()

  const allData = defineModel('allData')

  const state = $ref({
    tabLevel1: null,
    dataSource: [],

    loading: false,

    // 所有数据 在切换右侧一级时，保留之前的选中状态
    allSource: {},
  })

  const tabLevel1Code = computed(() => {
    return attrs.tabLevelOptions1.find(el => el.objectCategoryId === state.tabLevel1).objectCategoryCode
  })

  watch(
    () => attrs.tabLevelOptions1,
    newVal => {
      state.tabLevel1 = newVal[0].objectCategoryId
      changeTabLevel1(state.tabLevel1)
    },
  )

  const changeTabLevel1 = async val => {
    state.tabLevel1 = val
    state.loading = true
    await nextTick()
    objectCategoryCountByFirstLevel({ objectCategoryId: val }).then((res: any) => {
      state.dataSource = (res.data || []).map((el, idx) => ({
        ...el,
        checked: state.allSource?.[val]?.[idx]?.checked || false,
        indeterminate: state.allSource?.[val]?.[idx]?.indeterminate || false,
        items: el.items.map((ele, index) => ({
          ...ele,
          color: getColor(tabLevel1Code.value, el.objectCategoryCode, index),
          icon: getIcon(tabLevel1Code.value, el.objectCategoryCode, index, ele.code),
          checked: state.allSource?.[val]?.[idx]?.items?.[index]?.checked || false,
        })),
      }))

      state.allSource[val] = state.dataSource

      nextTick(() => {
        setTimeout(() => {
          state.loading = false
        }, 200)
      })
    })
  }

  const handleHeaderChecked = (val, el, idx) => {
    state.dataSource[idx].indeterminate = false
    state.dataSource[idx].items.forEach(ele => {
      ele.checked = val
    })

    state.allSource[state.tabLevel1] = state.dataSource

    allData.value = {
      ...state.allSource,
      tabLevel1: state.tabLevel1,
      objectCategoryCode: el.objectCategoryCode,
      code: 'check-all',
    }
  }
  const handleItemChecked = async (val, el, ele, idx, index) => {
    await nextTick()
    if (el.items.every(ele => ele.checked)) {
      state.dataSource[idx].checked = true
      state.dataSource[idx].indeterminate = false
    } else if (el.items.some(ele => ele.checked)) {
      state.dataSource[idx].checked = false
      state.dataSource[idx].indeterminate = true
    } else {
      state.dataSource[idx].checked = false
      state.dataSource[idx].indeterminate = false
    }

    state.allSource[state.tabLevel1] = state.dataSource

    allData.value = {
      ...state.allSource,
      tabLevel1: state.tabLevel1,
      objectCategoryCode: el.objectCategoryCode,
      code: ele.code,
    }
  }
</script>
<style lang="scss" scoped>
  .panel {
    position: absolute;
    right: 0.2rem;
    top: 0.81rem;
    bottom: 0.2rem;
    z-index: 999;
    height: calc(100% - 0.81rem - 0.2rem);
    width: 3.16rem;
    background-color: #ffffff;
    border-radius: 0.08rem;
    box-shadow: 0px 0.04rem 0.1rem 0px rgba(0, 0, 0, 0.1);
    border: 1px solid #f2f3f5;

    display: flex;
    flex-direction: column;
  }

  :deep(.n-tabs) {
    .v-x-scroll {
      .n-tabs-tab-pad {
        width: 0.1rem;
        height: 0.48rem;
      }
      .n-tabs-tab {
        font-size: 0.14rem;
      }
      .n-tabs-tab--active .n-tabs-tab__label {
        font-family: SourceHanSansCN-Medium;
        color: $text-main !important;
      }
      .n-tabs-wrapper {
        padding-left: 0.16rem;
      }
    }
  }

  :deep(.n-collapse) {
    .n-collapse-item {
      margin-top: 0.12rem;
      border-top: 1px solid transparent;

      .n-collapse-item__header {
        height: 0.32rem;
        font-size: 0.14rem;
        background: #f2f3f5;
        padding-top: 0;
        padding: 0.06rem 0.08rem 0.06rem 0.12rem;
        border-radius: 0.04rem;

        .n-collapse-item-arrow {
          margin-right: 0.04rem;
          font-size: 0.14rem;
        }
      }

      .n-collapse-item__content-inner {
        padding-top: 0;
      }
    }

    .active {
      .n-collapse-item__header {
        background: #d5e9fe;
      }
      .leaf {
        background: #e8f3ff;
      }
    }

    .n-collapse-item:first-child {
      margin-top: 0;
    }
  }

  :deep(.n-checkbox) {
    font-size: 0.14rem;
    .n-checkbox-box-wrapper {
      width: 0.16rem;
      .n-checkbox-box {
        width: 0.16rem;
        height: 0.16rem;
      }
    }
  }

  :deep(.n-spin) {
    font-size: 0.3rem;
  }
</style>
