<template>
  <div class="manage-unit">
    <a-tabs v-model="tabKey" size="small" @change="onTabChange">
      <a-tab-pane key="1" tab="巡查日历">
        <TrackReplay :projectId="projectId" />
      </a-tab-pane>
      <a-tab-pane key="2" tab="安全检查">
        <SafetyInspection :projectId="projectId" />
      </a-tab-pane>
      <a-tab-pane key="3" tab="沉降观测">
        <AttachItem :dataSource="currentData" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="jsx">
  import TrackReplay from './track-replay/index.vue'
  import SafetyInspection from './safety-inspection/index.vue'
  import AttachItem from './modules/attach-item.vue'
  import { getAttaches } from './services.js'

  export default {
    name: 'EngineInspection',
    components: { TrackReplay, SafetyInspection, AttachItem },
    props: {
      projectId: {},
    },
    data() {
      return {
        tabKey: '1',
        currentData: [],
      }
    },
    computed: {},
    watch: {},
    created() {},
    methods: {
      onTabChange(val) {
        if (val == 3) this.getList(val)
      },
      getList(type) {
        getAttaches({ type, projectId: this.projectId }).then(res => {
          this.currentData = res.data
        })
      },
    },
  }
</script>
<style lang="scss" scoped>
  .manage-unit {
    margin: 10px 0;
    width: 100%;
    height: calc(100% - 20px);
    overflow: hidden;
    overflow: auto;
  }

  ::v-deep .ant-tabs-top {
    height: 100%;
  }
  ::v-deep .ant-tabs-content {
    height: calc(100% - 50px) !important;
  }
</style>
