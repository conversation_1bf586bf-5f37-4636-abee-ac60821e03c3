<template>
  <div class="manage-unit">
    <a-tabs v-model="tabKey" size="small" @change="onTabChange">
      <a-tab-pane key="1" tab="基本信息">
        <BasicInfo :projectId="projectId" />
      </a-tab-pane>
      <!-- <a-tab-pane key="2" tab="工程附件">
        <Attach :projectId="projectId" />
      </a-tab-pane> -->
    </a-tabs>
  </div>
</template>

<script lang="jsx">
  import BasicInfo from './basicInfo.vue'
  import Attach from '../components/attach.vue'

  export default {
    name: 'Pump',
    components: {
      BasicInfo,
      Attach,
    },
    props: {
      projectId: {},
    },
    data() {
      return {
        tabKey: '1',
      }
    },
    computed: {},
    watch: {},
    created() {},
    methods: {
      onTabChange(val) {},
    },
  }
</script>
<style lang="scss" scoped>
  .manage-unit {
    margin: 10px 0;
    width: 100%;
    height: calc(100% - 20px);
    overflow: hidden;
  }

  ::v-deep .ant-tabs-top {
    height: 100%;
  }
  ::v-deep .ant-tabs-content {
    height: calc(100% - 35px) !important;
  }

  ::v-deep .ant-tabs-bar {
    margin-bottom: 0;
  }
</style>
