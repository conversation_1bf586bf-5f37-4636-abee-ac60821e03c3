<template>
  <div class="basic-info">
    <div class="content">
      <a-form
        ref="form"
        :model="form"
        :rules="rules"
        layout="horizontal"
        :labelCol="{ span: 12 }"
        :wrapperCol="{ span: 12 }"
        labelAlign="right"
      >
        <a-row :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">
              基本信息
              <a-button style="margin-left: 10px" type="primary" @click="onBtnClick">
                {{ this.type == 'detail' ? '编辑' : '确定' }}
              </a-button>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">水闸代码：</span>
              <span class="value">{{ data?.projectCode }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">水闸名称：</span>
              <span class="value">{{ data?.projectName }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="行政区划">
              <div class="form-value">{{ data.districtFullName }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">水闸经度(°)：</span>
              <span class="value">{{ data?.longitude }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">水闸维度(°)：</span>
              <span class="value">{{ data?.latitude }}</span>
            </div>
          </a-col>

          <a-col :span="24">
            <a-form-model-item label="水闸所在位置" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <div class="form-value" :title="data.location">{{ data.location }}</div>
            </a-form-model-item>
          </a-col>
          <!-- <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">水闸所在位置：</span>
              <span class="value" :title="data.location">{{ data?.location }}</span>
            </div>
          </a-col> -->
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="起点经度(°)">
              <a-input-number v-if="type == 'edit'" style="width: 100%" v-model="form.startLong" placeholder="请输入" />
              <div v-else class="form-value">{{ data.startLong }}</div>
            </a-form-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="起点纬度(°)">
              <a-input-number v-if="type == 'edit'" style="width: 100%" v-model="form.startLat" placeholder="请输入" />
              <div v-else class="form-value">{{ data.startLat }}</div>
            </a-form-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="终点经度(°)">
              <a-input-number v-if="type == 'edit'" style="width: 100%" v-model="form.endLong" placeholder="请输入" />
              <div v-else class="form-value">{{ data.endLong }}</div>
            </a-form-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="终点纬度(°)">
              <a-input-number v-if="type == 'edit'" style="width: 100%" v-model="form.endLat" placeholder="请输入" />
              <div v-else class="form-value">{{ data.endLat }}</div>
            </a-form-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title second-title">主要特征信息</div>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="水闸类型" prop="wagaType">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.wagaType"
                placeholder="请选择"
                :options="sluiceTypeOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ sluiceTypeOptions.find(el => el.value == data.wagaType)?.label }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="水闸用途" prop="wagaUse">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.wagaUse"
                placeholder="请选择"
                :options="sluicePurposeOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ sluicePurposeOptions.find(el => el.value == data.wagaUse)?.label }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="工程等别" prop="engGrad">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.engGrad"
                placeholder="请选择"
                :options="projectWaitOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ projectWaitOptions.find(el => el.value == data.engGrad)?.label }}
              </div>
            </a-form-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="工程规模" prop="engScal">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.engScal"
                placeholder="请选择"
                :options="projectScaleOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ projectScaleOptions.find(el => el.value == data.engScal)?.label }}
              </div>
            </a-form-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="主要建筑物级别" prop="mainBuildGrad">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.mainBuildGrad"
                placeholder="请选择"
                :options="mainBuildGradOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ mainBuildGradOptions.find(el => el.value == data.mainBuildGrad)?.label }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="设计最大过闸流量(m³/s)" prop="desLockDisc">
              <a-input-number
                v-if="type == 'edit'"
                style="width: 100%"
                v-model="form.desLockDisc"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.desLockDisc }}</div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="闸孔数量(孔)" prop="gaorNum">
              <a-input-number v-if="type == 'edit'" style="width: 100%" v-model="form.gaorNum" placeholder="请输入" />
              <div v-else class="form-value">{{ data.gaorNum }}</div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="闸孔总净宽(m)" prop="gaorTotNetWid">
              <a-input-number
                v-if="type == 'edit'"
                style="width: 100%"
                v-model="form.gaorTotNetWid"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.gaorTotNetWid }}</div>
            </a-form-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="装机流量(m³/s)" prop="insFlow">
              <a-input-number
                v-if="type == 'edit'"
                :precision="2"
                v-model="form.insFlow"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.insFlow }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="工程建设情况" prop="engStat">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.engStat"
                placeholder="请选择"
                :options="engStatOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ engStatOptions.find(el => el.value == data.engStat)?.label }}
              </div>
            </a-form-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="开工时间" prop="startDate">
              <a-date-picker
                v-if="type == 'edit'"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                v-model="form.startDate"
                placeholder="请选择"
                style="width: 100%"
              />
              <div v-else class="form-value">{{ data.startDate }}</div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="建成时间" prop="compDate">
              <a-date-picker
                v-if="type == 'edit'"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                v-model="form.compDate"
                placeholder="请选择"
                style="width: 100%"
              />
              <div v-else class="form-value">{{ data.compDate }}</div>
            </a-form-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="归口管理部门" prop="admDep">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.admDep"
                placeholder="请选择"
                :options="admDepOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ admDepOptions.find(el => el.value == data.admDep)?.label }}
              </div>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="备注" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <a-textarea v-if="type == 'edit'" class="area-text" v-model="form.note" placeholder="请输入" allowClear />
              <div v-else class="form-value">{{ data.note }}</div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </div>
</template>

<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getSluice, updateSluice } from './services'
  import UploadFile from '@/components/UploadFile/index.vue'

  export default {
    name: 'Sluice',
    components: { UploadFile },
    props: {
      projectId: {},
    },
    data() {
      return {
        data: {},
        sluiceTypeOptions: [],
        sluicePurposeOptions: [],
        projectWaitOptions: [],
        projectScaleOptions: [],
        mainBuildGradOptions: [],
        admDepOptions: [],
        engStatOptions: [],
        type: 'detail',
        form: {
          // projectId: undefined,
          // unitManagement: '',
          // wagaType: undefined,
          // wagaUse: undefined,
          // holeNumber: undefined,
          // totalWidth: undefined,
          // startWorkTime: '',
          // buildUpTime: '',
          // remarks: '',
        },
        rules: {},
      }
    },
    created() {
      this.init()

      this.getDataSource()
    },
    methods: {
      init() {
        //工程等别-projectWait 工程规模-projectScale 建筑物级别-mainBuildGrad 归口管理部门-admDep 工程建设情况-engStat

        getOptions('projectWait').then(res => {
          this.projectWaitOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('projectScale').then(res => {
          this.projectScaleOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('mainBuildGrad').then(res => {
          this.mainBuildGradOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('admDep').then(res => {
          this.admDepOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })

        getOptions('engStat').then(res => {
          this.engStatOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('sluiceType').then(res => {
          this.sluiceTypeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('sluicePurpose').then(res => {
          this.sluicePurposeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
      },
      getDataSource() {
        getSluice({ projectId: this.projectId }).then(res => {
          this.data = res.data
          this.form = {
            ...res.data,
            projectId: this.projectId,
            wagaType: res.data.wagaType ? `${res.data.wagaType}` : undefined,
            wagaUse: res.data.wagaUse ? `${res.data.wagaUse}` : undefined,

            engGrad: res.data.engGrad ? `${res.data.engGrad}` : undefined,
            engScal: res.data.engScal ? `${res.data.engScal}` : undefined,

            engStat: res.data.engStat ? `${res.data.engStat}` : undefined,

            mainBuildGrad: res.data.mainBuildGrad ? `${res.data.mainBuildGrad}` : undefined,
            admDep: res.data.admDep ? `${res.data.admDep}` : undefined,

            // sluiceType: res.data.sluiceType ? `${res.data.sluiceType}` : undefined,
            // sluicePurpose: res.data.sluicePurpose ? `${res.data.sluicePurpose}` : undefined,
          }
        })
      },

      onBtnClick() {
        if (this.type == 'edit') {
          const params = { ...this.form }
          // if (this.form.sluiceId) {
          updateSluice(params).then(res => {
            this.$message.success('修改成功', 3)
            this.getDataSource()
          })
          // } else {
          //   addSluice(params).then(res => {
          //     this.$message.success('新增成功', 3)
          //     this.getDataSource()
          //   })
          // }
        }

        this.type = this.type == 'detail' ? 'edit' : 'detail'
      },
    },
  }
</script>

<style lang="less" scoped>
  @import url('~@/global.less');

  @import url('~@/assets/styles/basic-info.less');
</style>
