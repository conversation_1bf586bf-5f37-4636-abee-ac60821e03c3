<template>
  <n-radio-group
    attrs
    :class="attrs.size === 'small' ? 'small-radio-group' : ''"
    style="
      background: #f2f3f5;
      display: flex;
      align-items: center;
      padding: 0 3px;
      border-radius: 4px;
      --n-button-box-shadow-focus: none;
    "
  >
    <n-radio-button v-for="(item, index) in attrs.options" :value="item.value" :label="item.label" />
  </n-radio-group>
</template>

<script setup name="MyBtnRadio">
  const attrs = useAttrs()
</script>

<style lang="scss" scoped>
  :deep(.n-radio-group) {
    background: #f2f3f5 !important;
    display: flex;
    align-items: center;
  }

  :deep(.n-radio-button) {
    border-color: transparent !important;
    height: calc(100% - 6px) !important;
    border-radius: 4px;
    background: transparent;
    .n-radio__label {
      height: 100%;
      display: flex;
      align-items: center;
    }
  }
  :deep(.n-radio-group__splitor) {
    height: calc(100% - 6px) !important;
    background: #f2f3f5;
    background-color: #f2f3f5 !important;
    width: 3px;
  }

  .small-radio-group {
    .n-radio-button {
      padding-left: 12px;
      padding-right: 12px;
    }
  }
</style>
