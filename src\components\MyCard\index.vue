<template><component :is="render()"></component></template>

<script setup lang="jsx" name="MyCard">
  const attrs = useAttrs()
  const slots = useSlots()

  const render = () => (
    <div
      class='p-20 border-rd-8px bg-#ffffff overflow-hidden flex'
      style={{ 'flex-direction': slots.headerLeft || attrs.title ? 'column' : 'row' }}
    >
      {(slots.headerLeft || attrs.title) && (
        <div flex flex-center-between>
          {slots.headerLeft
            ? slots.headerLeft()
            : attrs.title && <div class='font-[PingFangSCM] text-16'>{attrs.title}</div>}

          {slots.headerRight && slots.headerRight()}
        </div>
      )}

      {slots.default()}
    </div>
  )
</script>
