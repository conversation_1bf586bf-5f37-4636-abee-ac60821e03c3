<template>
  <div class="h-full w-full">
    <div class="w-full h-full relative">
      <n-spin v-if="state.loading" class="absolute-center z-9999999 size-full bg-[rgba(255,255,255,.2)]" />
      <Mapbox
        :onMapMounted="onMapMounted"
        :onMapStyleLoad="onMapStyleLoad"
        :mapZoom="state.zoom"
        :onMapZoomEnd="onMapZoomEnd"
        :onMapDragEnd="onMapDragEnd"
      />

      <GeoJsonPolygon
        v-for="layer in state.layers.MultiPolygon"
        :key="layer.id"
        v-bind="{ ...layer, ...attrs }"
        v-model:activeItem="activeItem"
        :zoom="state.zoom"
        @mounted="onLayerMounted"
      />

      <GeoJsonLine
        v-for="layer in state.layers.MultiLineString"
        :key="layer.id"
        v-bind="{ ...layer, ...attrs }"
        v-model:activeItem="activeItem"
        :zoom="state.zoom"
        @mounted="onLayerMounted"
      />

      <GeoJsonPoint
        v-for="layer in state.layers.Point"
        :key="layer.id"
        v-bind="{ ...layer, ...attrs }"
        v-model:activeItem="activeItem"
        :zoom="state.zoom"
        @mounted="onLayerMounted"
      />
    </div>

    <MapTool :mapIns="state.mapIns" :isShow="attrs.isShow" />

    <MapStyle ref="mapStyleRef" :mapIns="state.mapIns" :isShow="attrs.isShow" />
  </div>
</template>

<script setup lang="tsx" name="Map">
  import { unmountLoading } from '@/core/loading'
  import dayjs from 'dayjs'
  import axios from 'axios'
  import { createVNode, render } from 'vue'
  import { debounce } from 'lodash-es'
  import mapboxgl from 'mapbox-gl'
  import gcoord from 'gcoord'
  import * as turf from '@turf/turf'
  import { getValueByKey } from '@/api'
  import { listByIds } from '../services.ts'

  import GeoJsonPolygon from './layers/GeoJsonPolygon.vue'
  import GeoJsonLine from './layers/GeoJsonLine.vue'
  import GeoJsonPoint from './layers/GeoJsonPoint.vue'
  import useAreaDashLayer from './layers/useAreaDashLayer.ts'
  import overlayManager from './utils/overlayManager.js'

  import { dealAllPoint, mapBound, mapBoundGeo } from './utils/mapBounds.js'

  import MapTool from './components/tool.vue'
  import MapStyle from './components/mapStyle.vue'
  import PopupContent from './components/PopupContent.vue'

  import { colors, getColor, getIcon } from '../config.ts'
  import { getPulsingDot } from './utils/pulsingDot.js'
  import { clearSourceAndLayer } from './utils/mapUtils.js'

  const attrs = useAttrs()

  const activeTabLevel2 = defineModel('activeTabLevel2')
  const activeItem = defineModel('activeItem')

  const mapStyleRef = ref(null)

  const state = reactive({
    mapIns: null,
    zoom: 6,
    drawIns: null,

    districtGeojson: null,

    loading: false,
    overlays: [],
    layers: {
      MultiPolygon: [],
      MultiLineString: [],
      Point: [],
    },

    activeMarkerIns: null,

    pointsInRect: [],
    mapSiteIndexExpired: 0,
    expiredValue: null,
    showPopupZoom: 13.99,
    mapPopupIns: [],
    maxPopupIndex: 1,
  })

  useAreaDashLayer({
    mapIns: toRefs(state).mapIns,
    id: 'current-district',
    geojson: toRefs(state).districtGeojson,
    zoom: toRefs(state).zoom,
  })

  getValueByKey('map.site.index.expired').then(res => {
    state.mapSiteIndexExpired = +res.data
  })

  // 切换左侧二级
  watch([() => activeTabLevel2.value, () => state.mapIns], async newValArr => {
    if (!(!!newValArr[0] && !!newValArr[1])) return

    const newVal = newValArr[0]

    if (activeItem.value?.isLeftSource || activeItem.value?.isLeft) {
      if (activeItem.value?.tabVal2 !== newVal.objectCategoryCode) {
        activeItem.value = { gisLayer: activeItem.value.gisLayer }
      }
    }

    let lastActiveTabL2ObjCode = null
    Object.keys(state.layers).forEach(key => {
      state.layers[key].forEach(el => {
        if (el.isLeft) {
          lastActiveTabL2ObjCode = el.tabLevel2
        }
      })
    })

    // 删除处理
    if (lastActiveTabL2ObjCode) {
      state.loading = true

      // 使用 overlay 管理器移除图层
      overlayManager.removeLayer(`polygon-layer-${lastActiveTabL2ObjCode}`)
      overlayManager.removeLayer(`line-layer-${lastActiveTabL2ObjCode}`)
      overlayManager.removeLayer(`point-layer-${lastActiveTabL2ObjCode}`)
      state.overlays[lastActiveTabL2ObjCode] = null

      Object.keys(state.layers).forEach(key => {
        state.layers[key].some(element => element.id === lastActiveTabL2ObjCode) &&
          state.layers[key].splice(
            state.layers[key].findIndex(element => element.id === lastActiveTabL2ObjCode),
            1,
          )
      })

      await nextTick(() => {
        setTimeout(() => {
          state.loading = false
        }, 500)
      })
    }

    // 判断该二级图层中是否有已选中
    if (
      attrs.allData?.[newVal.leftTabLevel1]
        ?.find(elem => elem.objectCategoryCode === newVal.objectCategoryCode)
        ?.items?.some(elem => elem.checked)
    ) {
    } else {
      if (newVal.gisLayer && newVal.total) {
        state.loading = true

        axios(`${import.meta.env.VITE_GEOSERVER_BASE}${import.meta.env.VITE_GEOSERVER_URL}&typeName=${newVal.gisLayer}`).then(
          res => {
            const data = res.data || null

            // 把三级的数据带入properties
            data?.features?.forEach(elem => {
              elem.properties = {
                ...elem.properties,
                ...newVal.items.find(element => element.code === elem.properties.map_class),
                color: getColor(
                  newVal.leftTabLevel1Code,
                  newVal.objectCategoryCode,
                  newVal.items.findIndex(element => element.code === elem.properties.map_class),
                ),
                icon: getIcon(
                  newVal.leftTabLevel1Code,
                  newVal.objectCategoryCode,
                  newVal.items.findIndex(element => element.code === elem.properties.map_class),
                  newVal.items.find(element => element.code === elem.properties.map_class)?.code,
                ),
              }
            })

            if (!state.layers.Point?.length) {
              state.mapPopupIns.forEach(el => el.remove())
            }

            dealLayers(data, newVal, { tabLevel1: newVal.leftTabLevel1 }, true)
          },
        )
      }
    }
  })

  watch(
    () => attrs.allData,
    async (newVal: any) => {
      if (newVal === null) return
      const source = JSON.parse(JSON.stringify(newVal))

      const tabVal2 = source[newVal.tabLevel1].find(el => el.objectCategoryCode === newVal.objectCategoryCode)

      if (tabVal2?.gisLayer && tabVal2?.total) {
        const filter = tabVal2.items?.filter(ele => ele.checked && !!ele.count)

        tabVal2.layerFilter = filter
          ?.map(
            ele => `<PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>${ele.code}</Literal></PropertyIsEqualTo>`,
          )
          .join('')

        // 删除处理
        if (state.overlays[newVal.objectCategoryCode] && filter.length === 0) {
          state.loading = true

          // 使用 overlay 管理器移除图层
          overlayManager.removeLayer(`polygon-layer-${newVal.objectCategoryCode}`)
          overlayManager.removeLayer(`line-layer-${newVal.objectCategoryCode}`)
          overlayManager.removeLayer(`point-layer-${newVal.objectCategoryCode}`)
          state.overlays[newVal.objectCategoryCode] = null

          Object.keys(state.layers).forEach(key => {
            state.layers[key].some(element => element.id === tabVal2.objectCategoryCode) &&
              state.layers[key].splice(
                state.layers[key].findIndex(element => element.id === tabVal2.objectCategoryCode),
                1,
              )
          })

          await nextTick(() => {
            // 回到左边选中为主
            if (!tabVal2.items?.some(ele => ele.checked)) {
              if (activeTabLevel2.value.objectCategoryCode === newVal.objectCategoryCode) {
                if (activeItem.value.tabVal2 === activeTabLevel2.value.objectCategoryCode) {
                  activeItem.value = { ...activeItem.value, isLeft: true }
                }

                nextTick(() => {
                  activeTabLevel2.value = { ...activeTabLevel2.value }
                })
              } else {
                if (
                  activeItem.value?.tabVal2 === newVal.objectCategoryCode &&
                  !(activeItem.value?.isLeftSource || activeItem.value?.isLeft)
                ) {
                  activeItem.value = { gisLayer: activeItem.value.gisLayer }
                }
              }
            }

            setTimeout(() => {
              state.loading = false
            }, 500)
          })
        } else {
          // 回到左边选中为主
          if (!tabVal2.items?.some(ele => ele.checked)) {
            if (activeTabLevel2.value.objectCategoryCode === newVal.objectCategoryCode) {
              activeTabLevel2.value = { ...activeTabLevel2.value }
            }
          }
        }

        if (tabVal2.layerFilter && tabVal2.gisLayer) {
          state.loading = true

          axios(
            `${import.meta.env.VITE_GEOSERVER_BASE}${import.meta.env.VITE_GEOSERVER_URL}&typeName=${tabVal2.gisLayer}&filter=<Or>${tabVal2.layerFilter}</Or>`,
          ).then(res => {
            const data = res.data || null
            // 把三级的数据带入properties
            data?.features?.forEach(elem => {
              elem.properties = {
                ...elem.properties,
                ...filter.find(element => element.code === elem.properties.map_class),
              }
            })

            if (!state.layers.Point?.length) {
              state.mapPopupIns.forEach(el => el.remove())
            }
            dealLayers(data, tabVal2, newVal, false)
          })
        }
      }
    },
    // { deep: true },
  )

  const dealLayers = (data, tabVal2, allData, isLeft) => {
    if (data?.features?.[0]?.geometry?.type === 'MultiPolygon') {
      const currentIdx = state.layers.MultiPolygon.findIndex(element => element.tabLevel2 === tabVal2.objectCategoryCode)
      if (currentIdx > -1) {
        state.layers.MultiPolygon[currentIdx] = {
          mapIns: state.mapIns,
          geojson: data,
          id: tabVal2.objectCategoryCode,
          tabLevel1: allData.tabLevel1,
          tabLevel2: tabVal2.objectCategoryCode,
          isLeft: isLeft || false,
          gisLayer: tabVal2.gisLayer,
        }
      } else {
        state.layers.MultiPolygon.push({
          mapIns: state.mapIns,
          geojson: data,
          id: tabVal2.objectCategoryCode,
          tabLevel1: allData.tabLevel1,
          tabLevel2: tabVal2.objectCategoryCode,
          isLeft: isLeft || false,
          gisLayer: tabVal2.gisLayer,
        })
      }
    }
    if (data?.features?.[0]?.geometry?.type === 'MultiLineString') {
      state.loading = true

      const currentIdx = state.layers.MultiLineString.findIndex(element => element.tabLevel2 === tabVal2.objectCategoryCode)
      if (currentIdx > -1) {
        state.layers.MultiLineString[currentIdx] = {
          mapIns: state.mapIns,
          geojson: data,
          id: tabVal2.objectCategoryCode,
          tabLevel1: allData.tabLevel1,
          tabLevel2: tabVal2.objectCategoryCode,
          isLeft: isLeft || false,
          gisLayer: tabVal2.gisLayer,
        }
      } else {
        state.layers.MultiLineString.push({
          mapIns: state.mapIns,
          geojson: data,
          id: tabVal2.objectCategoryCode,
          tabLevel1: allData.tabLevel1,
          tabLevel2: tabVal2.objectCategoryCode,
          isLeft: isLeft || false,
          gisLayer: tabVal2.gisLayer,
        })
      }
    }
    if (data?.features?.[0]?.geometry?.type === 'Point') {
      state.loading = true

      const currentIdx = state.layers.Point.findIndex(element => element.tabLevel2 === tabVal2.objectCategoryCode)
      if (currentIdx > -1) {
        state.layers.Point[currentIdx] = {
          mapIns: state.mapIns,
          geojson: data,
          id: tabVal2.objectCategoryCode,
          tabLevel1: allData.tabLevel1,
          tabLevel2: tabVal2.objectCategoryCode,
          isLeft: isLeft || false,
          gisLayer: tabVal2.gisLayer,
        }
      } else {
        state.layers.Point.push({
          mapIns: state.mapIns,
          geojson: data,
          id: tabVal2.objectCategoryCode,
          tabLevel1: allData.tabLevel1,
          tabLevel2: tabVal2.objectCategoryCode,
          isLeft: isLeft || false,
          gisLayer: tabVal2.gisLayer,
        })
      }
    }

    setTimeout(() => {
      nextTick(() => {
        state.loading = false
        if (state.layers.Point?.length) {
          dealPointsInRect('pointDataChange')
        }
      })
    }, 500)
  }

  // 行政区划定位
  watch([() => attrs.currentDistrict, () => state.mapIns], newValArr => {
    if (!newValArr[0] || !newValArr[1]) return
    axios(
      `${import.meta.env.VITE_GEOSERVER_BASE}${import.meta.env.VITE_GEOSERVER_DISTRICT_URL}&filter=<PropertyIsEqualTo><PropertyName>code</PropertyName><Literal>${newValArr[0]}</Literal></PropertyIsEqualTo>`,
    ).then(res => {
      if (res?.data?.features?.length) {
        state.districtGeojson = res.data
        mapBoundGeo(res.data, state.mapIns, { top: 150, bottom: 150, left: 150, right: 150 })
      }
    })
  })

  // 选中某一条
  watch(
    () => activeItem.value,
    newVal => {
      clearSourceAndLayer(state.mapIns, ['dot-point'], ['layer-with-pulsing-dot'])

      if (!!newVal?.id) {
        axios(
          `${import.meta.env.VITE_GEOSERVER_BASE}${import.meta.env.VITE_GEOSERVER_URL}&typeName=${newVal.gisLayer}&filter=<PropertyIsEqualTo><PropertyName>object_id</PropertyName><Literal>${newVal.id}</Literal></PropertyIsEqualTo>`,
        ).then(res => {
          const data = res.data || null

          if (data?.features?.[0]?.geometry?.type === 'MultiPolygon' && newVal?.isLeftSource) {
            mapBoundGeo(data, state.mapIns, { top: 300, bottom: 300, left: 500, right: 500 })
          }
          if (data?.features?.[0]?.geometry?.type === 'MultiLineString' && newVal?.isLeftSource) {
            mapBoundGeo(data, state.mapIns, { top: 300, bottom: 300, left: 500, right: 500 })
          }

          if (data?.features?.[0]?.geometry?.type === 'Point') {
            if (newVal?.isLeftSource) {
              state.mapIns.easeTo({
                center: data?.features?.[0]?.geometry.coordinates, // 中心点
                zoom: 14, // 缩放比例
                duration: 1000, // 动画时长
              })
              setTimeout(() => {
                dealPointsInRect()
              }, 1500)
            }

            state.mapIns.addSource('dot-point', {
              type: 'geojson',
              data: data,
            })
            state.mapIns.addLayer({
              id: 'layer-with-pulsing-dot',
              type: 'symbol',
              source: 'dot-point',
              layout: {
                'icon-image': 'pulsing-dot',
              },
            })
          }
        })
      }
    },
  )

  // 飘窗
  watch(
    () => state.pointsInRect,
    newVal => {
      state.mapPopupIns.forEach(el => el.remove())

      newVal?.forEach(el => {
        const ins = new mapboxgl.Popup({
          closeOnClick: false,
          closeButton: true,
          offset: [0, -25],
          maxWidth: 'none',
          anchor: 'bottom',
        })

        const elPopup = document.createElement('div')
        const vNodePopup = createVNode(PopupContent, {
          ...el.properties,
          onClick: () => {
            state.maxPopupIndex += 1
            ins.getElement().style.zIndex = state.maxPopupIndex
          },
        })
        render(vNodePopup, elPopup)

        ins.setLngLat(el.geometry.coordinates).setDOMContent(elPopup).addTo(state.mapIns)

        state.mapPopupIns.push(ins)
      })
    },
  )

  const dealActivePoint = () => {
    if (!state.mapIns.getLayer('layer-with-pulsing-dot')) return
    const options = JSON.parse(JSON.stringify(state.mapIns.getSource('dot-point')._options))
    const dotLayer = JSON.parse(JSON.stringify(state.mapIns.getLayer('layer-with-pulsing-dot')))

    clearSourceAndLayer(state.mapIns, ['dot-point'], ['layer-with-pulsing-dot'])

    state.mapIns.addSource('dot-point', options)
    state.mapIns.addLayer(dotLayer)
  }

  const onLayerMounted = (objectCategoryCode, overlayManagerRef) => {
    // 现在所有图层都使用同一个 overlay 管理器，只需要记录引用
    state.overlays[objectCategoryCode] = overlayManagerRef
  }

  const onMapMounted = map => {
    state.mapIns = map

    // 初始化全局 overlay 管理器
    overlayManager.init(state.mapIns)

    const pulsingDot = getPulsingDot(state.mapIns, 150)
    state.mapIns.addImage('pulsing-dot', pulsingDot, { pixelRatio: 2 })
    nextTick(() => {
      setTimeout(() => {
        mapStyleRef.value.onMapStyleMounted()
        unmountLoading()
      }, 100)
    })
  }

  const onMapStyleLoad = map => {
    if (state.mapIns) {
      const pulsingDot = getPulsingDot(state.mapIns, 150)
      state.mapIns.addImage('pulsing-dot', pulsingDot, { pixelRatio: 2 })
      dealActivePoint()
    }
  }

  const onMapZoomEnd = (currentZoom, e) => {
    state.zoom = currentZoom

    if (e.originalEvent) {
      // 区别出fitBounds 导致的触发
      dealPointsInRect()
    } else {
      if (state.zoom < state.showPopupZoom) {
        state.mapPopupIns.forEach(el => el.remove())
      }
    }
  }

  const onMapDragEnd = () => {
    dealPointsInRect()
  }

  watch(
    () => attrs.isDataMode,
    newVal => {
      if (newVal) {
        dealPointsInRect()
      } else {
        state.mapPopupIns.forEach(el => el.remove())
      }
    },
  )

  const dealPointsInRect = (type?: string) => {
    if (!attrs.isDataMode) return
    if (state.zoom < state.showPopupZoom) {
      state.mapPopupIns.forEach(el => el.remove())

      return
    }

    const bound1 = state.mapIns.getBounds()
    // 把bound的padding设为0
    state.mapIns.fitBounds(
      [
        [bound1._ne.lng, bound1._ne.lat],
        [bound1._sw.lng, bound1._sw.lat],
      ],
      {
        animate: false,
        duration: 0,
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
      },
    )

    setTimeout(() => {
      // 此时bound是整个地图窗口大小
      const bound2 = state.mapIns.getBounds()

      const boundRect = {
        type: 'Feature',
        properties: {},
        geometry: {
          type: 'Polygon',
          coordinates: [
            [
              [bound2._ne.lng, bound2._ne.lat],
              [bound2._ne.lng, bound2._sw.lat],
              [bound2._sw.lng, bound2._sw.lat],
              [bound2._sw.lng, bound2._ne.lat],
              [bound2._ne.lng, bound2._ne.lat],
            ],
          ],
        },
      }

      const points = []
      state.layers.Point.forEach(el => {
        points.push(...turf.pointsWithinPolygon(el.geojson, boundRect).features)
      })

      const pointsInRect = JSON.parse(JSON.stringify(state.pointsInRect))
      let params
      if (state.expiredValue && dayjs().valueOf() < state.expiredValue) {
        params = points.filter(
          el => !pointsInRect.some(ele => `${el.properties.object_type}_${el.properties.object_id}` == ele.properties.objectUid),
        )
        if (params.length === 0 && type !== 'pointDataChange') {
          state.pointsInRect = JSON.parse(JSON.stringify(state.pointsInRect))
          state.expiredValue = dayjs().add(state.mapSiteIndexExpired, 'minute').valueOf()
          return
        }
        listByIds(params.map(ele => ({ objectId: ele.properties.object_id, objectType: ele.properties.object_type }))).then(
          res => {
            state.pointsInRect = (res.data || [])
              .map((ele, i) => ({
                ...params?.[i],
                properties: { ...params?.[i].properties, ...ele },
              }))
              .concat(
                pointsInRect.filter(el =>
                  points.some(ele => `${ele.properties.object_type}_${ele.properties.object_id}` == el.properties.objectUid),
                ),
              )
            state.expiredValue = dayjs().add(state.mapSiteIndexExpired, 'minute').valueOf()
          },
        )
      } else {
        params = points
        if (params.length === 0) {
          state.pointsInRect = JSON.parse(JSON.stringify(state.pointsInRect))
          state.expiredValue = dayjs().add(state.mapSiteIndexExpired, 'minute').valueOf()
          return
        }
        listByIds(params.map(ele => ({ objectId: ele.properties.object_id, objectType: ele.properties.object_type }))).then(
          res => {
            state.pointsInRect = (res.data || []).map((ele, i) => ({
              ...params?.[i],
              properties: { ...params?.[i].properties, ...ele },
            }))

            state.expiredValue = dayjs().add(state.mapSiteIndexExpired, 'minute').valueOf()
          },
        )
      }
    }, 300)
  }
</script>

<style lang="scss" scoped></style>
