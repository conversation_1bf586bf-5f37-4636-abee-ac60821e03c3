// 获取字典下拉选项
export function getOptions(dictCode) {
  return request({
    url: '/sys/dict/getOptions',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { dictCode },
  })
}

// 获取参数配置值
export function getValueByKey(configKey) {
  return request({
    url: '/sys/config/getValueByKey',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { configKey },
  })
}

//token是否有效
export function isTokenValid(data) {
  return request({
    url: '/sys/isTokenValid',
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data,
  })
}