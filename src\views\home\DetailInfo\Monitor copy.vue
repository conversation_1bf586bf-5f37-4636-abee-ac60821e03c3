<template>
  <div v-if="state.isShow" class="my-monitor-modal" :style="{ zIndex: monitorIndex }" @click="onClick">
    <header class="py-16px px-24px flex-center-between monitor-drag-area">
      <div class="title text-20px font-[SourceHanSansCN-Medium]">{{ state.title }}</div>
      <MyIcon class="i-material-symbols:close-small-outline-rounded text-28px cursor-pointer" @click="state.isShow = false" />
    </header>
    <div size-full v-if="!!url" class="px-16px pb-16px flex-1">
      <iframe :src="url" frameborder="0" class="wh-full"></iframe>
    </div>
  </div>
</template>
<script setup lang="tsx" name="Monitor">
  import { useUserStore } from '@/store'
  import { getValueByKey } from '@/api'
  import { initDrag } from '@/components/MyModal/utils'

  const attrs = useAttrs()

  const userStore = useUserStore()
  const monitorIndex = defineModel('monitorIndex')
  const videoIndex = defineModel('videoIndex')

  let url = $ref(null)
  const state = $ref({
    isShow: false,
    title: '',
  })

  const onClick = () => {
    monitorIndex.value = 9999
    videoIndex.value = 999
  }

  async function openModal(item) {
    state.isShow = true
    state.title = `${item.objectName}监测信息`

    videoIndex.value = 999
    monitorIndex.value = 9999

    nextTick(() => {
      // const initDragFn = new Function('return ' + initDrag.toString())()
      initDrag(document.querySelector('.monitor-drag-area'), document.querySelector('.my-monitor-modal'))
    })

    getValueByKey('site.index.detail.url').then(res => {
      url = `${res.data}?objectId=${item.objectId}&objectType=${item.objectType}&token=${userStore.token}&type=1`
    })
  }
  defineExpose({ openModal })
</script>

<style lang="scss" scoped>
  .my-monitor-modal {
    width: 85vw;
    height: 65vh;
    position: fixed;
    top: calc(50vh - 32.5vh);
    left: calc(50vw - 42.5vw);
    top: 0;
    z-index: 999;
    background-color: #ffffff;
    border-radius: 16px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow:
      0 6px 16px -9px rgba(0, 0, 0, 0.08),
      0 9px 28px 0 rgba(0, 0, 0, 0.05),
      0 12px 48px 16px rgba(0, 0, 0, 0.03);
  }
</style>
