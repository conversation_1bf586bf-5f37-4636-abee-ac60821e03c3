<template>
  <n-modal
    v-if="isRender"
    v-model:show="show"
    class="modal-box"
    :style="{ width: modalOptions.width, ...modalOptions.modalStyle }"
    :content-class="`${modalOptions.contentClass}`"
    :preset="undefined"
    size="huge"
    :bordered="false"
    @after-leave="onAfterLeave"
    v-bind="modalOptions"
  >
    <n-spin :show="modalOptions.loading" content-class="h-full">
      <n-card :style="modalOptions.contentStyle" :closable="modalOptions.closable" @close="close()">
        <template #header>
          <slot v-if="$slots.header" name="header" />
          <header v-else class="modal-header">{{ modalOptions.title }}</header>
        </template>

        <slot></slot>

        <!-- 底部按钮 -->
        <template #footer>
          <slot v-if="$slots.footer" name="footer" />
          <footer v-else-if="modalOptions.showFooter" class="flex justify-end">
            <n-button v-if="modalOptions.showCancel" @click="handleCancel()">
              {{ modalOptions.cancelText }}
            </n-button>
            <n-button
              v-if="modalOptions.showOk"
              type="primary"
              :loading="modalOptions.okLoading"
              class="ml-20"
              @click="handleOk()"
            >
              {{ modalOptions.okText }}
            </n-button>
          </footer>
        </template>
      </n-card>
    </n-spin>
  </n-modal>
</template>

<script setup>
  import { initDrag } from './utils'
  import { getCurrentInstance } from 'vue'

  const instance = getCurrentInstance()
  const slots = useSlots()
  const attrs = useAttrs()
  const props = defineProps({
    width: {
      type: String,
      // default: '800px',
    },
    closable: {
      type: Boolean,
      default: true,
    },
    cancelText: {
      type: String,
      default: '取消',
    },
    okText: {
      type: String,
      default: '确定',
    },
    showFooter: {
      type: Boolean,
      default: true,
    },
    showCancel: {
      type: Boolean,
      default: true,
    },
    showOk: {
      type: Boolean,
      default: true,
    },
    modalStyle: {
      type: Object,
      default: () => {},
    },
    contentStyle: {
      type: Object,
      default: () => {},
    },
    onOk: {
      type: Function,
      default: () => {},
    },
    onClose: {
      type: Function,
      default: () => {},
    },
    draggable: {
      type: Boolean,
      default: false,
    },
  })
  const isRender = ref(false)

  // 声明一个show变量，用于控制模态框的显示与隐藏
  const show = ref(false)
  // 声明一个modalOptions变量，用于存储模态框的配置信息
  const modalOptions = ref({})

  const title = computed({
    get() {
      return modalOptions.value.title
    },
    set(v) {
      modalOptions.value.title = v
    },
  })

  const loading = computed({
    get() {
      return !!modalOptions.value?.loading
    },
    set(v) {
      if (modalOptions.value) {
        modalOptions.value.loading = v
      }
    },
  })

  const okLoading = computed({
    get() {
      return !!modalOptions.value?.okLoading
    },
    set(v) {
      if (modalOptions.value) {
        modalOptions.value.okLoading = v
      }
    },
  })

  // 打开模态框
  async function open(options = {}) {
    isRender.value = open
    await nextTick()
    // 将props和options合并赋值给modalOptions
    modalOptions.value = { ...attrs, ...props, ...options }
    if (!modalOptions.value?.loading) {
      modalOptions.value.loading = false
    }

    // 将show的值设置为true
    show.value = true
    await nextTick()
    props.draggable &&
      initDrag(
        Array.prototype.at.call(document.querySelectorAll('.modal-header'), -1),
        Array.prototype.at.call(document.querySelectorAll('.modal-box'), -1),
      )
  }

  // 定义一个close函数，用于关闭模态框
  function close() {
    show.value = false
  }

  // 定义一个handleOk函数，用于处理模态框确定操作
  async function handleOk(data) {
    // 如果modalOptions中没有onOk函数，则直接关闭模态框
    if (typeof modalOptions.value.onOk !== 'function') {
      return close()
    }
    try {
      // 调用onOk函数，传入data参数
      const res = await modalOptions.value.onOk(data)
      // 如果onOk函数的返回值不为false，则关闭模态框
      // res !== false && close()
    } catch (error) {
      okLoading.value = false
      console.error(error)
    }
  }

  // 定义一个handleCancel函数，用于处理模态框取消操作
  async function handleCancel(data) {
    close()
  }

  async function onAfterLeave() {
    close()
    await nextTick()
    if (typeof modalOptions.value.onClose === 'function') {
      modalOptions.value.onClose()
    }
    await nextTick()
    isRender.value = false
    props.draggable &&
      initDrag(
        Array.prototype.at.call(document.querySelectorAll('.modal-header'), -1),
        Array.prototype.at.call(document.querySelectorAll('.modal-box'), -1),
      )
  }

  // 定义一个defineExpose函数，用于暴露open、close、handleOk、handleCancel函数
  defineExpose({
    open,
    close,
    handleOk,
    handleCancel,
    title,
    loading,
    okLoading,
    options: modalOptions,
  })
</script>

<style lang="scss" scoped>
  .modal-header {
    font-size: 20px;
    font-family: SourceHanSansCN-Medium;
  }
  :deep(.n-card) {
    border-radius: 16px;
  }
  :deep(.n-card__content) {
    overflow: auto;
  }

  :deep(.n-spin-content--spinning) {
    position: relative;
    opacity: 1 !important;
    &::before {
      content: '';
      width: 100%;
      height: 100%;
      position: absolute;
      background-color: rgba(200, 200, 200, 0.4);
      z-index: 1;
    }
  }
</style>

<style lang="scss">
  .n-scrollbar-content.n-modal-scroll-content > .n-spin-container.n-modal.modal-box {
    border-radius: 16px;
    overflow: hidden;
  }
</style>
