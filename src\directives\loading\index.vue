<script setup lang="ts">
  const title = ref('')
  const setTitle = (val: string) => {
    title.value = val
  }
  // defineProps({
  //   title: {
  //     type: String,
  //     default: '正在加载中...'
  //   }
  // })

  defineExpose({
    setTitle,
    title,
  })
</script>

<template>
  <div class="loading-box">
    <div class="loading-content">
      <n-spin>
        <span class="desc">{{ title }}</span>
      </n-spin>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .loading-box {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate3d(-50%, -50%, 0);
    .loading-content {
      text-align: center;

      .desc {
        line-height: 20px;
        font-size: 12px;
        color: #1989fa;
        position: relative;
      }
    }
  }
</style>
