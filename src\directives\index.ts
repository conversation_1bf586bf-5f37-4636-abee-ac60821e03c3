import type { App } from 'vue'
import loading from './loading/index'
import lazy from './lazy/index'
import permission from './permission/index'
import resize from './resize/index'
import drag from './drag'

export function setupDirectives(app: App) {
  app.directive('loading', loading)
  app.directive('lazy', lazy)
  app.directive('permission', permission)
  app.directive('resize', resize)
  app.directive('drag', drag)
}
