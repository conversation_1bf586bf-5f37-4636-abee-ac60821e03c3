<template>
  <div style="height:100%">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="合同名称">
        <a-input v-model="queryParam.contractName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>

      <!-- <a-form-item label="检查类型">
        <a-select show-search placeholder="请输入" v-model="queryParam.inspectionType" option-filter-prop="children">
          <a-select-option v-for="item in inspectionTypeOptions" :key="item.key" :value="item.key">
            {{ item.value }}
          </a-select-option>
        </a-select>
      </a-form-item> -->
      <a-form-item label="开始时间">
        <a-range-picker
          allow-clear
          :value="planTimes"
          format="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          @change="onRangeChange"
        />
      </a-form-item>

      <!-- <a-form-model-item label="所属工程">
        <a-tree-select
          v-model="queryParam.projectId"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :tree-data="projectOptions"
          show-search
          treeNodeFilterProp="title"
          allowClear
          placeholder="请选择"
          :replaceFields="{
            children: 'children',
            title: 'projectName',
            key: 'projectId',
            value: 'projectId'
          }"
          tree-default-expand-all
        ></a-tree-select>
      </a-form-model-item> -->
      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <!-- <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
            <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button>
          </div> -->
        </VxeTable>
        <FormDrawer
          v-if="showForm"
          ref="formRef"
          :inspectionTypeOptions="inspectionTypeOptions"
          :projectOptions="projectOptions"
          :propertyList="propertyList"
          @ok="onOperationComplete"
          @close="showForm = false"
        />
        <FormDetails
          v-if="showFormDetails"
          ref="formDetailsRef"
          :inspectionTypeOptions="inspectionTypeOptions"
          :projectOptions="projectOptions"
          :propertyList="propertyList"
          @ok="onOperationComplete"
          @close="showFormDetails = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getOptions, getProjectTree } from '@/api/common'
  import { getMaintenancePage, deleteMaintenance, getPropertyList } from './services'
  import FormDrawer from './modules/FormDrawer.vue'
  import FormDetails from './modules/FormDetails.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import getFlatTreeMap from '@/utils/getMapFlatTree'

  export default {
    name: 'PropertyManage',
    props: {
      projectId: {},
      projectName: {}
    },
    components: {
      VxeTable,
      VxeTableForm,
      FormDetails,
      FormDrawer
    },
    data() {
      return {
        isChecked: false,
        inspectionStatusOptions: [
          { key: 1, value: '未判定' },
          { key: 2, value: '正常' },
          { key: 3, value: '异常' }
        ],
        planTypeOptions: [
          { key: 1, value: '计划内' },
          { key: 2, value: '计划外' }
        ],
        propertyList: [],
        inspectionTypeOptions: [],
        inspectionTypes: [],
        inspectionStatuses: [],
        planTypes: [],
        projects: [],
        projectOptions: [],
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        showForm: false,
        showFormDetails: false,
        planTimes: [],
        archivesOptions: [],

        list: [],
        tableTitle: '物业管理',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          contractName: '',
          endTime: '',
          pageNum: 1,
          pageSize: 10,
          projectId: null,
          sort: [],
          startTime: ''
        },
        columns: [
          { type: 'checkbox', width: 30 },
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1 + (this.queryParam.pageNum - 1) * this.queryParam.pageSize
              }
            }
          },
          {
            title: '合同名称',
            field: 'contractName',
          },
          {
            title: '物业或外委单位',
            field: 'propertyName',
          },

          {
            title: '开始时间',
            field: 'startTime',
          },
          {
            title: '结束时间',
            field: 'endTime',
          },

          {
            title: '资金(万元)',
            field: 'propertyCapital',
          },
          // {
          //   title: '所属工程',
          //   field: 'projectName',
          //   align: 'center'
          // },

          {
            title: '操作',
            field: 'operate',
            width: 138,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetails(row)}>查看</a>
                  </span>
                )
              }
            }
          }
        ]
      }
    },
    created() {
      this.getList()
      getOptions('inspectionType').then(res => {
        this.inspectionTypeOptions = res.data
        this.inspectionTypes = getFlatTreeMap(this.inspectionTypeOptions, 'key')
      })

      getPropertyList({
        pageNum: 1,
        pageSize: Number.MAX_SAFE_INTEGER,
        projectId: null,
        propertyCharge: '',
        propertyName: ''
      }).then(res => {
        this.propertyList = res?.data?.data
      })

      // 获取工程树
      getProjectTree({ objectCategoryId: undefined }).then(res => {
        this.projectOptions = res.data
        this.projects = getFlatTreeMap(this.projectOptions, 'id')
      })
      this.planTypes = getFlatTreeMap(this.planTypeOptions, 'key')
      this.inspectionStatuses = getFlatTreeMap(this.inspectionStatusOptions, 'key')
    },
    mounted() {},
    methods: {
      // 弹出日历和关闭日历的回调
      openChangeOne(status) {
        if (status) {
          this.yearShowOne = true
        }
      },
      // 得到年份选择器的值
      panelChangeOne(value) {
        this.queryParam.year = moment(value).format('YYYY')
        this.yearShowOne = false
      },
      onRangeChange(value, planTimes) {
        this.planTimes = value
        if (planTimes.length == 0) {
          return
        }
        this.queryParam.startTime = planTimes[0] ? moment(planTimes[0]).format('YYYY-MM-DD') : undefined
        this.queryParam.endTime = planTimes[1] ? moment(planTimes[1]).format('YYYY-MM-DD') : undefined
      },
      /** 查询列表 */
      getList() {
        this.showForm = false
        this.showFormDetails = false
        this.loading = true
        this.selectChange({ records: [] })
        this.queryParam.projectId = this.projectId;
        getMaintenancePage(this.queryParam).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      //
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.maintenancePropertyId)
        this.names = valObj.records.map(item => item.maintenancePropertyName)
        this.isChecked = !!valObj.records.length
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          contractName: '',
          endTime: '',
          pageNum: 1,
          pageSize: 10,
          projectId: null,
          sort: [],
          startTime: ''
        }
        this.planTimes = []
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 导出
      handleExport() {},
      /* 新增 */
      handleAdd() {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle())
      },
      /* 修改 */
      handleEdit(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      /* 查看 */
      handleDetails(record) {
        this.showFormDetails = true
        this.$nextTick(() => this.$refs.formDetailsRef.details(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.maintenancePropertyId ? [row?.maintenancePropertyId] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteMaintenance({ maintenancePropertyIds: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {}
        })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      }
    }
  }
</script>
<style lang="less" scoped>
</style>
