<template>
  <BarEchart :dataSource="lineChart.dataSource" :custom="lineChart.custom" height="240px" />
</template>

<script lang="jsx">
  import BarEchart from '@/components/Echarts/bar-chanrt-horizontal.vue'

  export default {
    name: 'RainfallChart',
    components: { BarEchart },
    props: ['dataSource'],
    data() {
      return {
        lineChart: {
          dataSource: [],
          custom: {
            shortValue: true,
            legend: false
          }
        }
      }
    },
    watch: {
      dataSource: {
        handler(newVal, oldVal) {
          this.lineChart.dataSource = [
            {
              name: '次数',
              data: newVal?.map(el => [el.year, el.yearTrainingCount])
            }
          ]
        },
        deep: true
      }
    },
    created() {},
    methods: {}
  }
</script>

<style lang="less" scoped></style>
