<template>
  <!-- 增加修改 -->
  <ant-modal :visible="open" :modal-title="modalTitle" modalWidth="800" @cancel="cancel" modalHeight="800">
    <div slot="content" class="table-box">
      <VxeTable
        ref="vxeTableRef"
        :isShowTableHeader="false"
        :columns="columns"
        :tableData="list"
        :loading="false"
        :tablePage="false"
      ></VxeTable>

      <TrainingDetail v-if="showTrainingDetail" ref="trainingDetailRef" @close="showTrainingDetail = false" />
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getTrainingList } from '../services.js'
  import moment from 'moment'
  import AntModal from '@/components/pt/dialog/AntModal'
  import VxeTable from '@/components/VxeTable/index.vue'
  import UploadFile from '@/components/UploadFile/index.vue'
  import TrainingDetail from './trainingDetail.vue'

  export default {
    name: 'DetailModal',
    props: [],
    components: { AntModal, UploadFile, VxeTable, TrainingDetail },
    data() {
      return {
        showTrainingDetail: false,

        open: false,
        modalTitle: '',
        list: [],
        columns: [
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '年度',
            field: 'year',
            minWidth: 100,
          },
          {
            title: '培训主题',
            field: 'trainingName',
            minWidth: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <a-button type='link' onClick={() => this.onTrainingClick(row)}>
                    {row.trainingName}
                  </a-button>
                )
              },
            },
          },
          {
            title: '实际经费(万元)',
            field: 'actualExpense',
            minWidth: 120,
          },
          {
            title: '培训情况',
            field: 'statusImplementation',
            minWidth: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                return row.statusImplementation == 1 ? '完成' : '未完成'
              },
            },
          },
          {
            title: '培训时间',
            field: 'trainingTime',
            minWidth: 100,
          },
        ],
      }
    },
    filters: {},
    created() {},
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      /** 查看按钮操作 */
      handleDetail(record) {
        this.open = true
        this.modalTitle = '年度培训明细'

        getTrainingList({ year: record.year, projectId: record.projectId }).then(res => {
          this.list = res.data || []
          for (let i = 0; i < this.list.length; i++) {
            this.list[i].year = this.list[i].trainingTime.substring(0, 4)
          }
        })
      },

      onTrainingClick(row) {
        this.showTrainingDetail = true
        this.$nextTick(() => this.$refs.trainingDetailRef.handleDetail(row))
      },
    },
  }
</script>

<style lang="less" scoped>
  @import url('~@/global.less');

  ::v-deep .modal-content {
    width: 100%;
    height: 100%;
  }
  .table-box {
    width: 100%;
    height: 100%;
  }
</style>
