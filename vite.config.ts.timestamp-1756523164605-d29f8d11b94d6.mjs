// vite.config.ts
import { fileURLToPath, URL } from "node:url";
import { defineConfig, loadEnv } from "file:///D:/project/hehai/fe-water-baseline-onemap/node_modules/vite/dist/node/index.js";
import vue from "file:///D:/project/hehai/fe-water-baseline-onemap/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///D:/project/hehai/fe-water-baseline-onemap/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import ReactivityTransform from "file:///D:/project/hehai/fe-water-baseline-onemap/node_modules/@vue-macros/reactivity-transform/dist/vite.mjs";
import AutoImport from "file:///D:/project/hehai/fe-water-baseline-onemap/node_modules/unplugin-auto-import/dist/vite.js";
import Components from "file:///D:/project/hehai/fe-water-baseline-onemap/node_modules/unplugin-vue-components/dist/vite.js";
import { NaiveUiResolver } from "file:///D:/project/hehai/fe-water-baseline-onemap/node_modules/unplugin-vue-components/dist/resolvers.js";
import ViteCompression from "file:///D:/project/hehai/fe-water-baseline-onemap/node_modules/vite-plugin-compression/dist/index.mjs";
import vueSetupExtend from "file:///D:/project/hehai/fe-water-baseline-onemap/node_modules/vite-plugin-vue-setup-extend/dist/index.mjs";
import Unocss from "file:///D:/project/hehai/fe-water-baseline-onemap/node_modules/unocss/dist/vite.mjs";
import Icons from "file:///D:/project/hehai/fe-water-baseline-onemap/node_modules/unplugin-icons/dist/vite.js";
import IconsResolver from "file:///D:/project/hehai/fe-water-baseline-onemap/node_modules/unplugin-icons/dist/resolver.js";

// build/plugins/icons.js
import { globSync } from "file:///D:/project/hehai/fe-water-baseline-onemap/node_modules/glob/dist/esm/index.js";
import path from "path";
function getIcons() {
  const meFiles = globSync("src/assets/svgs/*.svg", { nodir: true, strict: true });
  const meIcons = meFiles.map((filePath) => {
    const fileName = path.basename(filePath);
    const fileNameWithoutExt = path.parse(fileName).name;
    return `i-me:${fileNameWithoutExt}`;
  });
  return [...meIcons];
}
var PLUGIN_ICONS_ID = "icons";
function pluginIcons() {
  return {
    name: PLUGIN_ICONS_ID,
    resolveId(id) {
      if (id === PLUGIN_ICONS_ID) return "\0" + PLUGIN_ICONS_ID;
    },
    load(id) {
      if (id === "\0" + PLUGIN_ICONS_ID) {
        return `export default ${JSON.stringify(getIcons())}`;
      }
    }
  };
}

// vite.config.ts
import { createSvgIconsPlugin } from "file:///D:/project/hehai/fe-water-baseline-onemap/node_modules/vite-plugin-svg-icons/dist/index.mjs";
var __vite_injected_original_import_meta_url = "file:///D:/project/hehai/fe-water-baseline-onemap/vite.config.ts";
var vite_config_default = ({ mode }) => {
  const isProd = mode === "prod";
  return defineConfig({
    plugins: [
      vue({ script: { propsDestructure: true } }),
      vueJsx({ enableObjectSlots: true }),
      // ref vs. 响应式变量  去掉.value写法
      ReactivityTransform(),
      Unocss(),
      AutoImport({
        imports: [
          "vue",
          "vue-router",
          "pinia",
          {
            "@vueuse/core": ["useDebounceFn", "useEventListener", "useFullscreen", "useMediaQuery", "useTitle", "useToggle"]
          },
          {
            from: "naive-ui",
            imports: [
              "DataTableBaseColumn",
              "DataTableColumn",
              "DataTableColumns",
              "DataTableCreateSummary",
              "DropdownOption",
              "FormInst",
              "FormItemInst",
              "FormItemRule",
              "FormRules",
              "FormValidationError",
              "MenuInst",
              "MenuOption",
              "UploadCustomRequestOptions",
              "UploadFileInfo",
              "UploadInst"
            ],
            type: true
          }
        ],
        include: [
          /\.[tj]sx?$/,
          // .ts, .tsx, .js, .jsx
          /\.vue$/,
          /\.vue\?vue/
          // .vue
        ],
        dirs: ["src/api/index**", "src/components/**", "src/hooks/**", "src/store/**", "src/store/**/**", "src/utils/**"],
        dts: "@types/auto-imports.d.ts",
        vueTemplate: true
        // 支持在 Vue 模版中使用
      }),
      Components({
        dts: "@types/components.d.ts",
        resolvers: [NaiveUiResolver(), IconsResolver()],
        types: [
          {
            from: "vue-router",
            names: ["RouterLink", "RouterView"]
          }
        ],
        directives: true,
        // 自动导入指令
        globs: ["src/components/common/*", "src/components/*/index.*", "src/layouts/*/index.vue"]
      }),
      Icons(),
      // 自定义插件，用于生成自定义icon，并添加到虚拟模块
      pluginIcons(),
      // svg icon
      createSvgIconsPlugin({
        // 指定图标文件夹
        iconDirs: [
          fileURLToPath(new URL("./src/assets/svgs/rightLevel2", __vite_injected_original_import_meta_url)),
          fileURLToPath(new URL("./src/assets/svgs", __vite_injected_original_import_meta_url))
        ],
        // 指定 symbolId 格式
        symbolId: "icon-[dir]-[name]"
      }),
      // 允许 setup 语法糖上添加组件名属性
      vueSetupExtend(),
      // gzip压缩
      ViteCompression({
        filter: /\.(js|mjs|json|css|html|ttf|otf|svg)$/i,
        algorithm: "gzip",
        threshold: 1024 * 5
        // 5k以上压缩,
      })
    ],
    // 环境变量
    define: {
      "import.meta.env.VITE_BASE_API": `"${process.env.npm_config_base_api || loadEnv(mode, process.cwd()).VITE_BASE_API}"`,
      "import.meta.env.VITE_CAS_URL": `"${process.env.npm_config_cas_url || loadEnv(mode, process.cwd()).VITE_CAS_URL}"`,
      "import.meta.env.VITE_TITLE": `"${process.env.npm_config_title || loadEnv(mode, process.cwd()).VITE_TITLE}"`,
      "import.meta.env.VITE_FAVICON": `"${process.env.npm_config_favicon || loadEnv(mode, process.cwd()).VITE_FAVICON}"`,
      "import.meta.env.VITE_TIANDI_BASE": `"${process.env.npm_config_tiandi_base || loadEnv(mode, process.cwd()).VITE_TIANDI_BASE}"`,
      "import.meta.env.VITE_TIANDI_TK": `"${process.env.npm_config_tiandi_tk || loadEnv(mode, process.cwd()).VITE_TIANDI_TK}"`,
      "import.meta.env.VITE_GEOSERVER_BASE": `"${process.env.npm_config_geoserver_base || loadEnv(mode, process.cwd()).VITE_GEOSERVER_BASE}"`,
      "import.meta.env.VITE_GEOSERVER_URL": `"${process.env.npm_config_geoserver_url || loadEnv(mode, process.cwd()).VITE_GEOSERVER_URL}"`,
      "import.meta.env.VITE_GEOSERVER_DISTRICT_URL": `"${process.env.npm_config_geoserver_district_url || loadEnv(mode, process.cwd()).VITE_GEOSERVER_DISTRICT_URL}"`
    },
    //这里进行配置别名
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", __vite_injected_original_import_meta_url)),
        // @代替src
        "~": fileURLToPath(new URL(__vite_injected_original_import_meta_url))
      },
      extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"]
    },
    optimizeDeps: {
      include: ["@iconify/json", "echarts"]
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: "modern-compiler",
          // or 'modern'
          additionalData: `@use "@/styles/variables.scss" as *;`,
          javascriptEnabled: true
        }
      }
    },
    build: {
      minify: "terser",
      terserOptions: {
        compress: {
          drop_console: isProd,
          // 是否过滤掉所有consol.log
          drop_debugger: isProd
        }
      },
      reportCompressedSize: false,
      chunkSizeWarningLimit: 1500,
      cssCodeSplit: true,
      rollupOptions: {
        output: {
          chunkFileNames: "static/js/[name]-[hash].js",
          entryFileNames: "static/js/[name]-[hash].js",
          assetFileNames: "static/[ext]/[name]-[hash].[ext]",
          manualChunks: {
            axios: ["axios"],
            "lodash-es": ["lodash-es"],
            "naive-ui": ["naive-ui"],
            vue: ["vue", "vue-router", "pinia", "@vueuse/core"]
          }
        }
      }
    },
    base: "/",
    server: {
      host: "0.0.0.0",
      port: 8080,
      open: true
      // proxy: {
      //   '/api': {
      //     target: loadEnv(mode, process.cwd()).VITE_BASE_API,
      //     changeOrigin: true,
      //     rewrite: (path: string) => path.replace(/^\/api/, ''),
      //     configure: (proxy, options: any) => {
      //       // 配置此项可在响应头中看到请求的真实地址
      //       proxy.on('proxyRes', (proxyRes, req) => {
      //         proxyRes.headers['x-real-url'] = new URL(req.url || '', options.target)?.href || ''
      //       })
      //     },
      //   },
      // },
    }
  });
};
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
