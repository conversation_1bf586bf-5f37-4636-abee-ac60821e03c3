<template>
  <div class="basic-info">
    <div class="content">
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        layout="horizontal"
        :labelCol="{ span: 12 }"
        :wrapperCol="{ span: 12 }"
        labelAlign="right"
      >
        <a-row :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">
              基本信息
              <a-button style="margin-left: 10px" type="primary" @click="onBtnClick" :loading="loading">
                {{ this.type == 'detail' ? '编辑' : '确定' }}
              </a-button>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">治河工程代码：</span>
              <span class="value">{{ data?.projectCode }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">治河工程名称：</span>
              <span class="value">{{ data?.projectName }}</span>
            </div>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="行政区划">
              <div class="form-value">{{ data.districtFullName }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="治河工程几何中心点经度(°)">
              <div class="form-value">{{ data.longitude }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="治河工程几何中心点维度(°)">
              <div class="form-value">{{ data.latitude }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="治河工程所在位置" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <div class="form-value" :title="data.location">{{ data.location }}</div>
            </a-form-model-item>
          </a-col>
          <!-- <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="治河工程所在位置">
              <div class="form-value" :title="data.location">{{ data.location }}</div>
            </a-form-model-item>
          </a-col> -->

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title second-title">主要特征信息</div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="工程数量级别">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.engNumLevel"
                placeholder="请选择"
                :options="engNumLevelOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ engNumLevelOptions.find(el => el.value == data.engNumLevel)?.label }}
              </div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="工程数量(处)">
              <a-input-number v-if="type == 'edit'" v-model="form.engNum" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.engNum }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="工程总长度(m)">
              <a-input-number v-if="type == 'edit'" v-model="form.engLen" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.engLen }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="被整治河段长度(m)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.mangReaLen"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.mangReaLen }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="岸别">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.bank"
                placeholder="请选择"
                :options="bankOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ bankOptions.find(el => el.value == data.bank)?.label }}
              </div>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="治河工程简介" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <a-input
                v-if="type == 'edit'"
                allowClear
                v-model="form.grpjBrin"
                class="area-text"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.grpjBrin }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :span="24">
            <a-form-model-item label="备注" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <a-textarea v-if="type == 'edit'" class="area-text" v-model="form.note" placeholder="请输入" allowClear />
              <div v-else class="form-value">{{ data.note }}</div>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
  </div>
</template>

<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getGrpj, updateGrpj } from './services'
  import { updateListFromStr } from '@/utils/util.js'
  export default {
    name: 'BasicInfo',
    components: {},
    props: {
      projectId: {},
    },
    data() {
      return {
        loading: false,
        data: {},

        bankOptions: [], //岸别
        engNumLevelOptions: [], //工程数量级别
        type: 'detail',
        form: {
          bank: '',
          engLen: undefined,
          engNum: undefined,
          engNumLevel: '',
          grpjBrin: '',
          id: undefined,
          mangReaLen: undefined,
          note: '',
          projectId: undefined,
        },
        rules: {},
      }
    },
    created() {
      this.init()
      this.getDataSource()
    },
    methods: {
      init() {
        getOptions('bank').then(res => {
          this.bankOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('engNumLevel').then(res => {
          this.engNumLevelOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
      },
      getDataSource() {
        getGrpj({ projectId: this.projectId }).then(res => {
          this.data = res.data
          this.form = {
            ...res.data,
            projectId: this.projectId,
            engNumLevel: res.data.engNumLevel ? res.data.engNumLevel : undefined,
            bank: res.data.bank ? res.data.bank : undefined,
          }
        })
      },

      onBtnClick() {
        if (this.type == 'edit') {
          this.form.projectId = this.projectId
          const params = { ...this.form }

          updateGrpj(params)
            .then(res => {
              this.$message.success('修改成功', 3)
              this.getDataSource()
            })
            .finally(() => (this.loading = false))
        }

        this.type = this.type == 'detail' ? 'edit' : 'detail'
      },
    },
  }
</script>

<style lang="scss" scoped>
 

  @import url('~@/assets/styles/basic-info.scss');
</style>
