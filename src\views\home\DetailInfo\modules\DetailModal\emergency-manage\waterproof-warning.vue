<template>
  <div class="tab-table-panel">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="年份">
        <a-date-picker
          mode="year"
          format="YYYY"
          v-model="queryParam.year"
          placeholder="请选择"
          allow-clear
          style="width: 240px"
          :open="yearShowOne"
          @openChange="openChangeOne"
          @panelChange="panelChangeOne"
          @keyup.enter.native="handleQuery"
        ></a-date-picker>
      </a-form-item>

      <a-form-item label="是否完成防水预警">
        <a-select show-search placeholder="请输入" v-model="queryParam.isReleaseWater" option-filter-prop="children">
          <a-select-option v-for="item in releaseWaterOptions" :key="item.key" :value="item.key">
            {{ item.value }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <!-- <a-form-item label="立卷时间">
        <a-range-picker
          allow-clear
          :value="buildTimes"
          format="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          @change="onRangeChange"
        />
      </a-form-item> -->

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button"></div>
        </VxeTable>

        <FormDetails
          v-if="showFormDetails"
          ref="formDetailsRef"
          :releaseWaterOptions="releaseWaterOptions"
          :projectOptions="projectOptions"
          @ok="onOperationComplete"
          @close="showFormDetails = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getOptions, getProjectTree } from '@/api/common'
  import { getReleasePage, deleteRelease } from './services'
  import FormDetails from './modules/WaterproofDetail.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import getFlatTreeMap from '@/utils/getMapFlatTree'

  export default {
    name: 'WaterproofWarning',
    components: {
      VxeTable,
      VxeTableForm,
      FormDetails
    },
    props: {
      projectId: {}
    },
    data() {
      return {
        unitList: [],
        unitArr: [],
        isChecked: false,
        releaseWaterOptions: [
          { key: 0, value: '否' },
          { key: 1, value: '是' }
        ], //是否完成放水预警(0否1是)
        releaseWaters: [],
        projects: [],
        projectOptions: [],
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        showForm: false,
        showFormDetails: false,
        buildTimes: [],
        archivesOptions: [],

        list: [],
        tableTitle: '放水预警',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          isReleaseWater: null,
          pageNum: 1,
          pageSize: 10,
          sort: [],
          year: null
        },
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '年份',
            field: 'year',
            minWidth: 80
          },
          {
            title: '通知对象',
            field: 'noticePeople',
            minWidth: 100
          },
          {
            title: '通知时间',
            field: 'noticeTime',
            minWidth: 100
          },
          {
            title: '通知发出人',
            field: 'noticeOut',
            minWidth: 100
          },
          {
            title: '反馈时间',
            field: 'feedbackTime',
            minWidth: 150
          },
          {
            title: '反馈人姓名',
            field: 'feedbackName',
            minWidth: 100
          },
          {
            title: '是否完成防水预警',
            field: 'isReleaseWater',
            minWidth: 140,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.releaseWaters[row.isReleaseWater]?.value || ''
              }
            }
          },
          {
            title: '所属工程',
            field: 'projectName',
            minWidth: 140
          },
          {
            title: '操作',
            field: 'operate',
            width: 80,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetails(row)}>查看</a>
                  </span>
                )
              }
            }
          }
        ]
      }
    },
    created() {
      this.getList()
      this.releaseWaters = getFlatTreeMap(this.releaseWaterOptions, 'key')

      // 获取工程树
      getProjectTree({ objectCategoryId: undefined }).then(res => {
        this.projectOptions = res.data
        this.projects = getFlatTreeMap(this.projectOptions, 'id')
      })
    },
    mounted() {},
    methods: {
      // 弹出日历和关闭日历的回调
      openChangeOne(status) {
        if (status) {
          this.yearShowOne = true
        }
      },
      // 得到年份选择器的值
      panelChangeOne(value) {
        this.queryParam.year = moment(value).format('YYYY')
        this.yearShowOne = false
      },
      onRangeChange(value, buildTimes) {
        this.buildTimes = value
        if (buildTimes.length == 0) {
          return
        }
        this.queryParam.startTime = buildTimes[0] ? moment(buildTimes[0]).format('YYYY-MM-DD') + ' 00:00:00' : undefined
        this.queryParam.endTime = buildTimes[1] ? moment(buildTimes[1]).format('YYYY-MM-DD') + ' 23:59:59' : undefined
      },
      /** 查询列表 */
      getList() {
        this.showForm = false
        this.showFormDetails = false
        this.loading = true
        this.selectChange({ records: [] })
        getReleasePage({ ...this.queryParam, projectId: this.projectId }).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.releaseWaterId)
        this.names = valObj.records.map(item => item.releaseWaterName)
        this.isChecked = !!valObj.records.length
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          isReleaseWater: null,
          pageNum: 1,
          pageSize: 10,
          sort: [],
          year: null
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      /* 查看 */
      handleDetails(record) {
        this.showFormDetails = true
        this.$nextTick(() => this.$refs.formDetailsRef.details(record))
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      }
    }
  }
</script>
<style lang="scss" scoped>
  .tab-table-panel {
    height: 100%;
  }
</style>
