// CAS登录
export function casLogin(data) {
  return request({
    url: '/sys/casLogin',
    method: 'post',
    data,
  })
}

// 获取用户信息
export function getUser(userId) {
  return request({
    url: '/sys/user/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { userId },
  })
}

// 获取当前登录用户信息
export function getUserProfile() {
  return request({
    url: '/sys/user/profile',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 获取登录用户菜单权限
export function getMenuPermissions() {
  return request({
    url: '/sys/user/getMenuPermissions',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 登出
export function logout() {
  return request({
    url: '/sys/logout',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 用户列表
export function userList(data) {
  return request({
    url: '/sys/user/list',
    method: 'post',
    data,
  })
}
