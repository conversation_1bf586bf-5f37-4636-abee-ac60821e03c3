import dayjs from 'dayjs'

/**
 * 获取assets/images下的图片地址
 * 若有中间文件夹,应传入如 animals/dog.png
 */
export function getImageUrl(name: string) {
  return new URL(`/src/assets/images/${name}`, import.meta.url).href
}

// localStorage
export function storageGet<K extends keyof T>(key: K): T[K] | null {
  const json = localStorage.getItem(key)
  if (json) {
    let storageData: T[K] | null = null
    try {
      storageData = JSON.parse(json)
    } catch {}

    if (storageData) {
      return storageData
    }
  }
  localStorage.removeItem(key)
  return null
}
export function storageSet(key, value) {
  const json = JSON.stringify(value)
  localStorage.setItem(key, json)
}

export function timeFix() {
  const time = new Date()
  const hour = time.getHours()
  return hour < 9 ? '早上好' : hour <= 11 ? '上午好' : hour <= 13 ? '中午好' : hour < 20 ? '下午好' : '晚上好'
}

// /assets/svgs下的所有svg文件
export function getLocalIcons() {
  const svgIcons = import.meta.glob('/src/assets/svgs/*.svg')

  const keys = Object.keys(svgIcons)
    .map(item => item.split('/').at(-1)?.replace('.svg', '') || '')
    .filter(Boolean)

  return keys
}

// 判断是否禁用日期
export function isDisabledDate(current) {
  if (dayjs(current).valueOf() > dayjs().valueOf()) return true

  if (
    dayjs(current).valueOf() <
    dayjs()
      .subtract(storageGet('allSetting')?.complementDay + 1, 'day')
      .valueOf()
  )
    return true

  return false
}
