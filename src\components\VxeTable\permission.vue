<template>
  <div class="vxe-table-content" ref="tableContentRef">
    <a-spin class="spin-loading" v-if="loading" :spinning="loading"></a-spin>

    <div :class="['header-bar', 'small']" v-if="isShowTableHeader">
      <div class="title">
        <template v-if="tableTitle">
          {{ tableTitle }}
        </template>
        <slot v-else-if="$slots.title" name="title"></slot>
        <template v-else>高级表格</template>
      </div>
      <div class="button">
        <slot name="button"></slot>
      </div>

      <div class="topSet" v-if="isShowSetBtn">
        <a-tooltip title="刷新" v-if="isShowRefresh">
          <a-icon @click="refresh" class="action" :type="loading ? 'loading' : 'reload'" />
        </a-tooltip>
        <action-size @input="sSizeChange" v-model="sSize" class="action" v-if="isShowSize" />
        <action-columns
          v-show="isShowColumns"
          :myColumns="myColumns"
          :originColumns="originColumns"
          @reset="onColumnsReset"
          @setColumnHeight="setColumnHeight()"
          @changeColumns="changeColumns"
          ref="columnHeightRef"
          class="action"
        >
          <template :slot="slot" v-for="slot in slots">
            <slot :name="slot"></slot>
          </template>
        </action-columns>

        <a-tooltip title="全屏" v-if="isShowFull">
          <a-icon @click="fullscreenHandle" class="action" :type="fullScreen ? 'fullscreen-exit' : 'fullscreen'" />
        </a-tooltip>
      </div>
    </div>

    <div class="vxe-table-box">
      <div class="vxe-table-box-content" :style="{ position: autoHeight ? 'static' : 'absolute' }">
        <VxeGrid
          :border="border"
          :height="height"
          :loading="false"
          :size="sSize"
          ref="vxeTableRef"
          class="sortable-column-demo"
          @checkbox-change="valObj => $listeners.selectChange && $listeners.selectChange(valObj)"
          @checkbox-all="valObj => $listeners.selectChange && $listeners.selectChange(valObj)"
          @sort-change="onSortChange"
          :sort-config="{ remote: true, multiple: true, trigger: 'cell' }"
          :checkbox-config="checkboxConfig"
          :row-config="rowConfig"
          :column-config="{ resizable: true, useKey: true }"
          :seq-config="{ startIndex: (tablePage.pageNum - 1) * tablePage.pageSize }"
          :columns="myColumns"
          :data="tableData"
          :style="!!tablePage ? {} : { paddingBottom: '10px' }"
          v-bind="{ ...$attrs, ...$props }"
          v-on="$listeners"
        >
          <template #pager v-if="!!tablePage">
            <slot name="footer"></slot>

            <VxePager
              border
              ref="vxePagerRef"
              size="small"
              :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'Sizes']"
              :page-sizes="pageSize"
              :pager-count="3"
              :total="tablePage.total"
              :current-page.sync="tablePage.pageNum"
              :page-size.sync="tablePage.pageSize"
              @page-change="onPageChange"
            >
              <template #left>
                <span>
                  <!-- 第 {{ (tablePage.pageNum - 1) * tablePage.pageSize + 1 }}-{{
                    tablePage.pageNum * tablePage.pageSize > tablePage.total
                      ? tablePage.total
                      : tablePage.pageNum * tablePage.pageSize
                  }}
                  条， -->
                  <!-- 共 {{ tablePage.total }} 条 -->
                </span>
              </template>
            </VxePager>
          </template>

          <template #empty>
            <a-empty />
          </template>
        </VxeGrid>
      </div>
    </div>
  </div>
</template>

<script>
  import ActionSize from './ActionSize'
  import ActionColumns from './ActionColumns'
  import Sortable from 'sortablejs'
  import cloneDeep from 'lodash.clonedeep'
  import * as _ from 'lodash'

  export default {
    name: 'VxeTable',
    components: { ActionSize, ActionColumns },
    props: {
      tableTitle: String,
      columns: Array,
      tableData: Array,
      tablePage: [Boolean, Object],
      loading: { type: Boolean, default: false },
      border: { default: 'inner' },
      size: { type: String, default: 'medium' },
      rowConfig: { type: Object, default: () => ({ isHover: true }) },
      checkboxConfig: { type: Object, default: () => ({ highlight: true, showHeader: true }) },
      height: { default: 'auto' },

      isShowTableHeader: { type: Boolean, default: true }, // 是否显示头部
      isShowSetBtn: { type: Boolean, default: true }, // 是否显示设置
      isShowRefresh: { type: Boolean, default: true }, // 是否显示刷新
      isShowSize: { type: Boolean, default: true }, // 是否显示密度
      isShowColumns: { type: Boolean, default: true }, // 是否显示列配置
      isShowFull: { type: Boolean, default: true }, // 是否全屏

      autoHeight: { default: false }, // 自己撑开高度
      isDrop: { type: Boolean, default: true }, //是否支持拖动
      isDataChangeReload: { default: false }, // data改变,表格重载
      isAdaptPageSize: { default: false }, // 默认值 是否启用自适应pageSize
      otherHeight: { default: 0 }, // 自适应pageSize时 其他元素的高度
    },
    data() {
      const pageSize = [
        { label: '10条/页', value: 10 },
        { label: '20条/页', value: 20 },
        { label: '30条/页', value: 30 },
        { label: '40条/页', value: 40 },
        { label: '100条/页', value: 100 },
        { label: '300条/页', value: 300 },
        { label: '500条/页', value: 500 },
      ]
      if (this.isAdaptPageSize) {
        pageSize.unshift({ label: '自适应', value: 10 })
      }
      return {
        dealHeight: 0,
        sSize: this.size,
        myColumns: [],
        originColumns: [],
        fullScreen: false,
        filterRowNum: 1,

        pageSize: pageSize,
        tableHeight: undefined,
        adaptPageSize: 10,
        isAdaptPageSizeData: this.isAdaptPageSize,
      }
    },
    computed: {
      slots() {
        return Object.keys(this.$slots).filter(slot => slot !== 'title')
      },
      clientHeight() {
        return this.$store?.state?.size?.clientHeight
      },
    },
    watch: {
      tableData: {
        handler(newVal) {
          if (this.isDataChangeReload) {
            this.$refs.vxeTableRef.reloadData(newVal)
          }
        },
        deep: true,
      },
      columns: {
        handler(newVal) {
          const cols = newVal.map(el => {
            if (el.type == 'seq' || el.type == 'checkbox') {
              return { ...el, fixed: 'left' }
            }
            if (el.field == 'operate') {
              return { ...el, fixed: 'right' }
            }
            return el
          })

          this.myColumns = cloneDeep(cols)

          this.originColumns = cloneDeep(cols)
        },
        deep: true,
        immediate: true,
      },
      clientHeight: {
        handler(newVal, oldVal) {
          let height = newVal
          height = height - 50 - 40 - 30 // 头部 导航 上下padding
          if (this.tablePage) {
            height = height - 56 // 翻页器与底部留白
          }
          if (this.isShowTableHeader) {
            if (this.$slots?.button?.[0]?.children?.length > 0) {
              height = height - 61 // 有button
            } else {
              height = height - 53 // 无button
            }
          }
          height = height - this.otherHeight
          this.tableHeight = height // 此时保存在组件

          this.dealSize()
        },
        immediate: true,
      },
    },
    created() {},
    mounted() {
      // 支持手动拖动列位置
      this.isDrop && this.columnDrop()
      // 支持手动拖动行
      this.$listeners.afterRowSort && this.rowDrop()

      document.addEventListener('fullscreenchange', this.toggleScreen)
    },
    updated() {},
    beforeDestroy() {
      if (this.sortableIns) {
        this.sortableIns.destroy()
      }

      document.removeEventListener('fullscreenchange', this.toggleScreen)
    },
    methods: {
      onChangeHeight(filterRowNum) {
        this.filterRowNum = filterRowNum
        this.dealSize()
      },

      sSizeChange(type) {
        this.sSize = type
        this.dealSize()
      },

      dealSize() {
        if (!this.isAdaptPageSizeData) return
        const height = this.tableHeight - (16 + 46 * this.filterRowNum)
        let num
        switch (this.sSize) {
          case 'medium':
            num = Math.floor((height - 42) / 44)
            break
          case 'small':
            num = Math.floor((height - 40) / 40)
            break
          case 'mini':
            num = Math.floor((height - 36) / 36)
            break
          default:
        }
        this.adaptPageSize = num < 1 ? 1 : num
        this.$emit('adaptPageSizeChange', this.adaptPageSize)
        this.pageSize = this.pageSize.map((el, i) => (i === 0 ? { label: '自适应', value: this.adaptPageSize } : el))
      },

      // // 刷新
      refresh() {
        // this.$refs.vxeTableRef.reloadData(this.tableData).then(() => {
        // this.clearSort()
        this.$emit('refresh')
        // })
      },

      // 切换全屏
      toggleScreen() {
        if (document.fullscreenElement === null) {
          if (this.fullScreen) {
            this.fullScreen = false
          }
        }
      },

      // 全屏
      fullscreenHandle() {
        this.fullScreen = !this.fullScreen

        if (this.fullScreen) {
          const el = this.$refs.tableContentRef
          if (el.requestFullscreen) {
            el.requestFullscreen()
            return true
          } else if (el.webkitRequestFullScreen) {
            el.webkitRequestFullScreen()
            return true
          } else if (el.mozRequestFullScreen) {
            el.mozRequestFullScreen()
            return true
          } else if (el.msRequestFullscreen) {
            el.msRequestFullscreen()
            return true
          }
        } else {
          if (document.exitFullscreen) {
            document.exitFullscreen()
          } else if (document.webkitCancelFullScreen) {
            document.webkitCancelFullScreen()
          } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen()
          } else if (document.msExitFullscreen) {
            document.msExitFullscreen()
          }
        }
      },

      // 设置列按钮重置
      onColumnsReset(conditions) {
        this.$emit('reset', conditions)
      },

      // 设置列的弹窗的高度
      setColumnHeight() {
        let height = this.$refs.tableContentRef.offsetHeight
        if (height > 450) {
          height = 450
        }
        this.$refs.columnHeightRef.setHeight(height)
      },

      changeColumns(columns) {
        this.myColumns = cloneDeep(columns)
        this.$refs.vxeTableRef.loadColumn(this.myColumns).then(() => {
          // 重置后排序保持原来状态
          this.$refs.vxeTableRef.sort(this.myColumns.map(el => ({ ...el, order: el?.order ? el.order : null })))
        })
      },

      onPageChange(page) {
        const obj = this.$refs.vxePagerRef.$children?.[0] || {}

        if (page.type === 'size' && obj.currentOption?.label === '自适应') {
          this.isAdaptPageSizeData = true
          this.dealSize()
          return
        }
        if (page.type === 'size' && obj.currentOption?.label !== '自适应') {
          this.isAdaptPageSizeData = false
        }
        this.$listeners.handlePageChange(page)
      },

      onSortChange(valObj) {
        this.originColumns = this.originColumns.map(el => {
          if (el.field == valObj?.field) {
            return { ...el, ...valObj.column }
          }
          return el
        })
        const { fullColumn } = this.$refs.vxeTableRef.getTableColumn()
        this.myColumns = fullColumn
        this.$listeners.sortChange && this.$listeners.sortChange(valObj)
      },

      // clearSort() {
      //   this.$refs.vxeTableRef.clearSort()
      // },

      // 处理展开
      setTreeExpand(keys, idCode, callback) {
        this.$nextTick(() => {
          const vxeTableIns = this.$refs.vxeTableRef

          let arr = [...keys]
          function dealParentRow(id) {
            const parentRow = vxeTableIns.getParentRow(`${id}`)
            if (parentRow) {
              arr.push(parentRow[idCode])
              dealParentRow(parentRow[idCode])
            }
          }
          keys.forEach(id => dealParentRow(id))

          arr = [...new Set(arr)]
          const rows = arr.map(key => vxeTableIns.getRowById(`${key}`))
          vxeTableIns.setTreeExpand(rows, true).then(() => {
            callback && callback()
          })
        })
      },

      // 列拖拽
      columnDrop() {
        this.$nextTick(() => {
          const $table = this.$refs.vxeTableRef
          this.sortableIns = Sortable.create(
            $table.$el.querySelector('.body--wrapper>.vxe-table--header .vxe-header--row'),
            {
              handle: '.vxe-header--column .vxe-cell',
              animation: 300,
              delay: 100,
              // 停靠位置样式
              ghostClass: 'vxe-table-sortable-ghost',
              // 拖动对象移动样式
              dragClass: 'vxe-table-sortable-drag',
              // 禁用html5原生拖拽行为
              forceFallback: true,

              onEnd: ({ item, newIndex, oldIndex }) => {
                const { fullColumn, tableColumn } = $table.getTableColumn()

                const targetThElem = item
                const wrapperElem = targetThElem.parentNode
                const newColumn = fullColumn[newIndex] //目标的
                const oldColumn = fullColumn[oldIndex] //拖动的

                if (newColumn.fixed || oldColumn.fixed) {
                  const oldThElem = wrapperElem.children[oldIndex]
                  // 错误的移动
                  if (newIndex > oldIndex) {
                    wrapperElem.insertBefore(targetThElem, oldThElem)
                  } else {
                    wrapperElem.insertBefore(targetThElem, oldThElem ? oldThElem.nextElementSibling : oldThElem)
                  }
                  this.$message.warn('固定列不允许拖动，即将还原操作！')
                  return
                }
                // 获取列索引 columnIndex > fullColumn
                const oldColumnIndex = $table.getColumnIndex(tableColumn[oldIndex])
                const newColumnIndex = $table.getColumnIndex(tableColumn[newIndex])
                // 移动到目标列
                const currRow = fullColumn.splice(oldColumnIndex, 1)[0]
                fullColumn.splice(newColumnIndex, 0, currRow)
                this.myColumns = cloneDeep(fullColumn)
                $table.loadColumn(this.myColumns)
              },
            },
          )
        })
      },

      // 行拖拽
      rowDrop() {
        this.$nextTick(() => {
          const Tbody = this.$refs.vxeTableRef
          Sortable.create(Tbody.$el.querySelector('.body--wrapper>.vxe-table--body tbody'), {
            handle: '.drag-btn',
            delay: 50,
            forceFallback: true,
            animation: 300,
            //拖拽结束后执行的方法
            onEnd: ({ newIndex, oldIndex }) => {
              const midData = JSON.parse(JSON.stringify(this.tableData))
              const currRow = midData.splice(oldIndex, 1)[0]
              midData.splice(newIndex, 0, currRow)

              let arr = []
              midData.forEach(el => {
                const { _X_ROW_KEY, ...elseObj } = el
                arr.push(elseObj)
              })

              this.$refs.vxeTableRef.reloadData([])

              //排序完成后
              this.$nextTick(() => {
                setTimeout(() => {
                  this.$emit('afterRowSort', arr)
                })
              })
            },
          })
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .vxe-table-content {
    border-radius: 2px;
    background-color: #fff;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;

    .spin-loading {
      position: absolute;
      left: 50%;
      transform: translate(-50%);
      top: 30%;
      z-index: 999;
    }

    .vxe-table-box {
      flex: 1;
      width: 100%;
      position: relative;
      .vxe-table-box-content {
        position: absolute;
        height: calc(100% - 15px);
        width: calc(100% - 20px);
        margin: 0 10px;
      }
    }

    .header-bar {
      padding: 16px 24px;
      display: flex;
      align-items: center;
      border-radius: 4px;
      transition: all 0.3s;
      &.small {
        padding: 14px 14px 14px;
        border: 1px solid #fff;
        border-bottom: 0;
        .title {
          // font-size: 16px;
        }
      }
      .title {
        font-size: 16px;
        font-weight: 700;
        transition: all 0.3s;
        color: rgba(0, 0, 0, 0.65);
      }
      .button {
        flex: 1;
        display: flex;
        justify-content: flex-end;
        align-items: center;
      }
      .topSet {
        > .action {
          font-size: 16px;
          margin: 0 4px;
          padding: 0 4px;
        }
      }
    }
  }

  ::v-deep .vxe-table-sortable-ghost {
    background-color: #dfecfb;
  }

  ::v-deep .vxe-table-sortable-drag {
    background-color: #fbfbfb;
    border-radius: 5px;
    box-shadow: 0 0 10px #000;
    width: auto !important;
  }

  ::v-deep .sortable-column-demo .vxe-header--row .vxe-header--column.col--fixed {
    cursor: no-drop;
  }

  // 选中行颜色
  ::v-deep .vxe-table--render-default .vxe-body--row.row--checked {
    background-color: #e6f7ff;
  }

  // 拖拽范围
  ::v-deep .vxe-table--render-default.size--medium .vxe-header--column:not(.col--ellipsis) {
    padding: 0;
    .vxe-cell {
      padding: 10px;
    }
  }
  // icon颜色
  ::v-deep .vxe-table--render-default .vxe-tree--node-btn {
    color: #1890ff;
  }

  // 多选框美化
  ::v-deep .vxe-table--render-default {
    .vxe-cell--checkbox .vxe-checkbox--icon {
      color: #c0c0c0;
      font-weight: 500;
    }
    .is--checked.vxe-cell--checkbox .vxe-checkbox--icon {
      color: #1890ff;
    }
    .is--indeterminate.vxe-cell--checkbox .vxe-checkbox--icon {
      color: #1890ff;
    }
  }

  // // 调整默认单元格高度
  // ::v-deep  .vxe-table--render-default.size--medium .vxe-body--row {
  //   height: 57px;
  //   .vxe-body--column {
  //     padding: 6px 0;
  //   }
  // }

  // table最底部的线
  ::v-deep .vxe-table--render-default .vxe-table--border-line {
    border: none;
  }

  // 翻页器高度
  .vxe-pager.size--small {
    margin-top: 2px;
  }
  // ::v-deep  .vxe-select .size--small .vxe-pager--sizes {
  //   width: 100px !important;
  // }
  // ::v-deep  .vxe-pager .vxe-pager--sizes {
  //   width: 100px !important;
  // }
</style>
