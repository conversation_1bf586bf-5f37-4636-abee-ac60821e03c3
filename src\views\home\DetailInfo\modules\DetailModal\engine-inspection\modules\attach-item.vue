<template>
  <div class="attach-box">
    <div class="content">
      <a-empty v-if="!dataSource.length" style="flex: 1" />

      <template v-else>
        <div class="attach-item" v-for="(el, i) in dataSource" :key="i">
          <div class="img"></div>
          <div class="name">{{ el.attachName }}</div>
          <a-button size="small" type="primary" @click="() => onClick(el)">查看详情</a-button>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="jsx">
  export default {
    name: 'AttachItem',
    components: {},
    props: {
      dataSource: { default: () => [] }
    },
    data() {
      return {}
    },
    computed: {},
    watch: {},
    created() {},
    methods: {
      onClick(el) {
        window.open(el.attachUrl)
      }
    }
  }
</script>
<style lang="scss" scoped>
  .attach-box {
    height: 100%;
    margin: 0 10px;
    overflow: auto;

    .content {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 10px;
    }
  }

  .attach-item {
    border: 1px solid #ddd;
    border-radius: 5px;
    display: flex;
    width: 150px;
    padding-bottom: 10px;

    flex-direction: column;
    align-items: center;
    .img {
      background: url('~@/assets/images/file-occupied.png') no-repeat;
      background-size: 100% 100%;
      width: 100%;
      height: 100px;
    }
    .name {
      width: 100%;
      // height: 30px;
      display: -webkit-box;
      overflow: hidden;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      text-overflow: ellipsis;
      text-align: center;
      margin: 8px 0;
    }
  }
</style>
