<template>
  <!-- 增加修改 -->
  <ant-modal :visible="open" :modal-title="modalTitle" modalWidth="600" @cancel="cancel" modalHeight="700">
    <div slot="content">
      <a-row :gutter="32">
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title">基本信息</div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">降等报废名称：</label>
            <span class="common-value-text">{{ data.scrapName }}</span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">开始时间：</label>
            <span class="common-value-text">{{ data.startTime }}</span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">管理单位：</label>
            <span class="common-value-text">{{ data.unitManagement }}</span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">鉴定单位：</label>
            <span class="common-value-text" :title="data.authenticateUnit">{{ data.authenticateUnit }}</span>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="item">
            <label class="common-label-text">验收单位：</label>
            <span class="common-value-text">{{ data.acceptanceUnit }}</span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">所属工程：</label>
            <span class="common-value-text">{{ data.projectName }}</span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="item">
            <label class="common-label-text">发起原因：</label>
            <span class="common-value-text">{{ data.reason }}</span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title">鉴定单位</div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div style="margin-bottom: 20px">
            <div
              class="file-item"
              v-for="(el, i) in data.unitIdentificationAttaches"
              :key="i"
              @click="() => downLoad(el.attachUrl)"
            >
              <a-icon type="paper-clip" />
              <div class="file-name" :title="el.attachName" style="margin-left: 5px">{{ el.attachName }}</div>
            </div>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title">验收单位</div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div style="margin-bottom: 20px">
            <div
              class="file-item"
              v-for="(el, i) in data.unitAcceptanceAttaches"
              :key="i"
              @click="() => downLoad(el.attachUrl)"
            >
              <a-icon type="paper-clip" />
              <div class="file-name" style="margin-left: 5px">{{ el.attachName }}</div>
            </div>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title">结果</div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">完成情况：</label>
            <span class="common-value-text">
              {{ data.scrapStatus ? scrapStatusOptions.find(el => el.key == data.scrapStatus).value : '' }}
            </span>
          </div>
        </a-col>
      </a-row>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getScrap } from '../services'
  import moment from 'moment'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'DetailModal',
    props: ['scrapStatusOptions'],
    components: { AntModal },
    data() {
      return {
        open: false,
        modalTitle: '',
        data: {},
      }
    },
    filters: {},
    created() {},
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      /** 查看按钮操作 */
      handleDetail(record) {
        this.open = true
        this.modalTitle = '查看'
        getScrap({ scrapId: record.scrapId }).then(res => {
          this.data = res.data
        })
      },
      downLoad(url) {
        window.open(url)
      },
    },
  }
</script>

<style lang="scss" scoped>
 

  .title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
  }
  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .file-item {
    display: flex;
    align-items: center;
    line-height: 25px;
  }

  .file-name {
    color: #1890ff;
    cursor: pointer;
    margin-right: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
</style>
