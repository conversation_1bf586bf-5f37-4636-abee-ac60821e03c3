<template>
  <div class="basic-info">
    <div class="content">
      <a-row :gutter="32">
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title">
            基本信息
            <a-button style="margin-left: 10px" type="primary" @click="onBtnClick" :loading="loading">
              {{ this.type == 'detail' ? '编辑' : '确定' }}
            </a-button>
          </div>
        </a-col>
        <a-col :lg="8" :md="8" :sm="24">
          <div class="item">
            <span class="label">跌水代码：</span>
            <span class="value">{{ data?.projectCode }}</span>
          </div>
        </a-col>
        <a-col :lg="8" :md="8" :sm="24">
          <div class="item">
            <span class="label">跌水名称：</span>
            <span class="value">{{ data?.projectName }}</span>
          </div>
        </a-col>

        <a-col :lg="8" :md="8" :sm="24">
          <div class="item">
            <span class="label">行政区划：</span>
            <span class="value">{{ data?.districtFullName }}</span>
          </div>
        </a-col>
        <!-- <a-col :lg="8" :md="8" :sm="24">
          <div class="item">
            <span class="label">跌水所在位置：</span>
            <span class="value" :title="data.location">{{ data?.location }}</span>
          </div>
        </a-col> -->

        <a-col :lg="8" :md="8" :sm="24">
          <div class="item">
            <span class="label">跌水经度(°)：</span>
            <span class="value">{{ data?.longitude }}</span>
          </div>
        </a-col>
        <a-col :lg="8" :md="8" :sm="24">
          <div class="item">
            <span class="label">跌水维度(°)：</span>
            <span class="value">{{ data?.latitude }}</span>
          </div>
        </a-col>
        <a-col :span="24">
          <a-form-model-item label="跌水所在位置" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
            <div class="form-value" :title="data.location">{{ data.location }}</div>
          </a-form-model-item>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title second-title">主要特征信息</div>
        </a-col>

        <a-form-model
          ref="form"
          :model="form"
          :rules="rules"
          layout="horizontal"
          :labelCol="{ span: 12 }"
          :wrapperCol="{ span: 12 }"
          labelAlign="right"
        >
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="结构形式">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.structForm"
                placeholder="请选择"
                :options="structFormOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ structFormOptions.find(el => el.value == data.structForm)?.label }}
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="角度(°)">
              <a-input-number
                v-if="type == 'edit'"
                :precision="2"
                v-model="form.angle"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.angle }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="桩号">
              <a-input v-if="type == 'edit'" v-model="form.stakeMark" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.stakeMark }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="已运行年限">
              <a-input-number
                v-if="type == 'edit'"
                :precision="0"
                v-model="form.runYear"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.runYear }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="现状评估结论">
              <a-input v-if="type == 'edit'" v-model="form.evalConclusion" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.evalConclusion }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="备注" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <a-textarea v-if="type == 'edit'" class="area-text" v-model="form.note" placeholder="请输入" allowClear />
              <div v-else class="form-value">{{ data.note }}</div>
            </a-form-model-item>
          </a-col>
        </a-form-model>
      </a-row>
    </div>
  </div>
</template>

<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getDrop, updateDrop } from './services'

  export default {
    name: 'BasicInfo',
    components: {},
    props: {
      projectId: {},
    },
    data() {
      return {
        loading: false,
        data: {},
        structFormOptions: [],

        engTaskOptions: [],
        projectWaitOptions: [],
        projectScaleOptions: [],
        mainBuildGradOptions: [],
        engStatOptions: [],
        admDepOptions: [],

        sluiceTypeOptions: [],
        sluicePurposeOptions: [],
        type: 'detail',
        form: {
          // admDep: '',
          // compDate: '',
          // desHead: undefined,
          // desLockDisc: undefined,
          // engGrad: undefined,
          // engScal: null,
          // engStat: '',
          // engTask: undefined,
          // gaorNum: null,
          // gaorTotNetWid: undefined,
          // id: undefined,
          // insFlow: undefined,
          // insPow: undefined,
          // mainBuildGrad: '',
          // note: '',
          // projectId: undefined,
          // pumpNum: undefined,
          // pustType: undefined,
          // startDate: '',
          // wagaType: undefined,
          // wagaUse: '',

          angle: undefined,
          evalConclusion: '',
          id: undefined,
          note: '',
          projectId: undefined,
          runYear: undefined,
          stakeMark: '',
          structForm: '',
        },
        rules: {},
      }
    },
    created() {
      this.init()

      this.getDataSource()
    },
    methods: {
      init() {
        //结构形式	-structForm
        getOptions('steepStructureForm').then(res => {
          this.structFormOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
      },
      getDataSource() {
        getDrop({ projectId: this.projectId }).then(res => {
          this.data = res.data
          this.form = {
            ...res.data,
            projectId: this.projectId,
            structForm: res.data.structForm ? `${res.data.structForm}` : undefined,
          }
        })
      },
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      onBtnClick() {
        if (this.type == 'edit') {
          this.form.projectId = this.projectId
          const params = { ...this.form }
          updateDrop(params)
            .then(res => {
              this.$message.success('修改成功', 3)
              this.getDataSource()
            })
            .finally(() => (this.loading = false))
        }

        this.type = this.type == 'detail' ? 'edit' : 'detail'
      },
    },
  }
</script>

<style lang="less" scoped>
  @import url('~@/global.less');

  @import url('~@/assets/styles/basic-info.less');
</style>
