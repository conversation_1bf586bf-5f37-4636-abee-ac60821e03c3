import { MapboxOverlay } from '@deck.gl/mapbox'

/**
 * 全局 Overlay 管理器
 * 解决 WebGL 上下文限制问题，所有图层共享同一个 overlay
 */
class OverlayManager {
  constructor() {
    this.overlay = null
    this.mapInstance = null
    this.layers = new Map() // 存储所有图层
    this.isInitialized = false
  }

  /**
   * 初始化 overlay
   * @param {Object} mapInstance - Mapbox 地图实例
   */
  init(mapInstance) {
    if (this.isInitialized && this.mapInstance === mapInstance) {
      return this.overlay
    }

    // 如果已经有 overlay，先移除
    if (this.overlay && this.mapInstance) {
      this.mapInstance.removeControl(this.overlay)
    }

    this.mapInstance = mapInstance
    this.overlay = new MapboxOverlay({
      id: 'global-deck-overlay',
      layers: []
    })

    this.mapInstance.addControl(this.overlay)
    this.isInitialized = true

    return this.overlay
  }

  /**
   * 添加或更新图层
   * @param {string} layerId - 图层唯一标识
   * @param {Object} layer - Deck.gl 图层对象
   */
  addLayer(layerId, layer) {
    if (!this.overlay) {
      console.warn('Overlay not initialized. Call init() first.')
      return
    }

    this.layers.set(layerId, layer)
    this.updateOverlay()
  }

  /**
   * 移除图层
   * @param {string} layerId - 图层唯一标识
   */
  removeLayer(layerId) {
    if (!this.overlay) {
      console.warn('Overlay not initialized.')
      return
    }

    this.layers.delete(layerId)
    this.updateOverlay()
  }

  /**
   * 获取图层
   * @param {string} layerId - 图层唯一标识
   */
  getLayer(layerId) {
    return this.layers.get(layerId)
  }

  /**
   * 检查图层是否存在
   * @param {string} layerId - 图层唯一标识
   */
  hasLayer(layerId) {
    return this.layers.has(layerId)
  }

  /**
   * 更新 overlay 的图层列表
   */
  updateOverlay() {
    if (!this.overlay) return

    const layerArray = Array.from(this.layers.values())
    this.overlay.setProps({
      layers: layerArray
    })
  }

  /**
   * 清空所有图层
   */
  clearAllLayers() {
    this.layers.clear()
    this.updateOverlay()
  }

  /**
   * 销毁管理器
   */
  destroy() {
    if (this.overlay && this.mapInstance) {
      this.mapInstance.removeControl(this.overlay)
    }
    
    this.overlay = null
    this.mapInstance = null
    this.layers.clear()
    this.isInitialized = false
  }

  /**
   * 获取所有图层的数量
   */
  getLayerCount() {
    return this.layers.size
  }

  /**
   * 获取所有图层的 ID 列表
   */
  getLayerIds() {
    return Array.from(this.layers.keys())
  }
}

// 创建全局单例实例
const overlayManager = new OverlayManager()

export default overlayManager
