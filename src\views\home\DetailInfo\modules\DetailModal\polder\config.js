export const projectInfo = [
  {
    title: '基本信息',
    indicators: [
      { label: '圩垸代码', key: 'projectCode', type: 'detail', value: '' },
      { label: '圩垸名称', key: 'projectName', type: 'detail', value: '' },
      { label: '行政区划', key: 'districtFullName', type: 'detail', value: '' }, //圩垸所在位置
      { label: '经度', key: 'longitude', type: 'detail', value: '' },
      { label: '纬度', key: 'latitude', type: 'detail', value: '' },
    ],
  },
  // {
  //   title: '圩区堤防',
  //   indicators: [
  //     { label: '堤防名称', key: 'dikeName', type: 'input', value: '' },
  //     { label: '堤防级别', key: 'dikeWait', type: 'select', options: 'dikeWaitOptions', value: '' },
  //     { label: '堤防起点位置', key: 'dikeStartAddress', type: 'input', value: '' },
  //     { label: '堤防终点位置', key: 'dikeEndAddress', type: 'input', value: '' },
  //     { label: '起点坐标(北纬)', key: 'dikeStartNorthernLatitude', type: 'input-number', value: '' },
  //     { label: '起点坐标(东经)', key: 'dikeStartNorthernLongitude', type: 'input-number', value: '' },
  //     { label: '终点坐标(北纬)', key: 'dikeEndEastLatitude', type: 'input-number', value: '' },
  //     { label: '终点坐标(东经)', key: 'dikeEndEastLongitude', type: 'input-number', value: '' },
  //     { label: '堤防长度(千米)', key: 'dikeLength', type: 'input-number', value: '' },
  //     { label: '设计防洪标准(年)', key: 'designStandard', type: 'input-number', value: '' },
  //     { label: '设计洪水位(米)', key: 'designWaterLevel', type: 'input-number', value: '' }
  //     // {
  //     //   label: '是否有全景图',
  //     //   key: 'isPanorama',
  //     //   type: 'radio',
  //     //   options: [
  //     //     { label: '是', value: '1' },
  //     //     { label: '否', value: '0' }
  //     //   ],
  //     //   value: ''
  //     // }
  //   ]
  // },
  {
    title: '特征信息',
    indicators: [
      { label: '右上角经度', key: 'upRightLong', type: 'input-number', value: '' },
      { label: '右上角纬度', key: 'upRightLat	', type: 'input-number', value: '' },
      { label: '左下角经度', key: 'lowLeftLong', type: 'input-number', value: '' },
      { label: '左下角纬度', key: 'lowLeftLat', type: 'input-number', value: '' },

      // { label: '所属工程', key: 'projectId', type: 'select', options: 'poldClasOptions', value: '' },
      { label: '圩垸分类', key: 'poldClas', type: 'select', options: 'poldClasOptions', value: '' },
      { label: '平垸性质', key: 'plodProp', type: 'select', options: 'plodPropOptions', value: '' },
      { label: '设计行洪流量(m³/s)', key: 'decFlFlow', type: 'input-number', value: '' },
      { label: '设计蓄洪量(10⁴m³)', key: 'desStorCap', type: 'input-number', value: '' },
      { label: '运用原则', key: 'opPr', type: 'input', value: '' },
      { label: '备注', key: 'note', type: 'input', value: '' },

      // { label: '管理单位', key: 'unitManagement', type: 'input', value: '' },
      // { label: '圩区面积(万亩)', key: 'polderArea', type: 'input-number', value: '' },
      // { label: '工程等别', key: 'projectWait', type: 'select', options: 'projectWaitOptions', value: '' },
      // { label: '工程规模', key: 'projectCale', type: 'select', options: 'projectScaleOptions', value: '' },
      // { label: 'Ⅱ级圩区数量(个)', key: 'polderLevelNumber', type: 'input-number', value: '' },
      // { label: '圩区内水闸数量(个)', key: 'sluiceNumber', type: 'input-number', value: '' },
      // { label: '圩区内过闸流量大于100m³/s的水闸数量(个)', key: 'sluicePassNumber', type: 'input-number', value: '' },
      // { label: '圩区内大中型泵站数量(个)', key: 'pumpNumber', type: 'input-number', value: '' },
      // { label: '参照水位站名称', key: 'referenceWaterName', type: 'input', value: '' },
      // { label: '参照水位站编码', key: 'referenceWaterCode', type: 'input', value: '' },
      // { label: '参照水位站警戒水位(米)', key: 'referenceVigilanceLevel', type: 'input-number', value: '' },
      // { label: '参照水位站危急水位(米)', key: 'referenceDangerLevel', type: 'input-number', value: '' },
    ],
  },
]
