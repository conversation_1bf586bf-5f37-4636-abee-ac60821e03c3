<template>
  <!-- <div>安全管理</div> -->
  <div class="manage-unit">
    <a-tabs v-model="tabKey" size="small">
      <a-tab-pane key="1" tab="注册备案">
        <RegisterFiling :projectId="projectId" />
      </a-tab-pane>
      <a-tab-pane key="2" tab="安全鉴定">
        <SafetyIdentification :projectId="projectId" />
      </a-tab-pane>
      <a-tab-pane key="3" tab="除险加固">
        <Reinforcement :projectId="projectId" />
      </a-tab-pane>
      <a-tab-pane key="4" tab="降等报废">
        <DemotionScrap :projectId="projectId" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
<script lang="jsx">
  import RegisterFiling from './RegisterFiling.vue'
  import SafetyIdentification from './SafetyIdentification.vue'
  import Reinforcement from './Reinforcement.vue'
  import DemotionScrap from './DemotionScrap.vue'

  export default {
    name: 'CustodialF<PERSON>',
    props: {
      projectId: {},
      projectName: {},
    },
    components: {
      RegisterFiling,
      SafetyIdentification,
      Reinforcement,
      DemotionScrap,
    },
    data() {
      return {
        tabKey: '1',
      }
    },
    computed: {},
    watch: {},
    created() {},
    methods: {},
  }
</script>
<style lang="scss" scoped>
  .manage-unit {
    margin: 10px 0;
    width: 100%;
    height: calc(100% - 20px);
    overflow: hidden;
    overflow: auto;
  }
  ::v-deep .ant-tabs-top {
    height: 100%;
  }
  ::v-deep .ant-tabs-content {
    height: calc(100% - 50px) !important;
  }
</style>
