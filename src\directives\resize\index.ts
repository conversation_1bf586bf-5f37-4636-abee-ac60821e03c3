const directive = {
  beforeMount(el, binding) {
    // 创建一个ResizeObserver实例来监听元素大小的变化
    const resizeObserver = new ResizeObserver(entries => {
      for (let entry of entries) {
        if (typeof binding.value === 'function') {
          // 调用绑定值作为函数，传递entry给该函数
          binding.value(entry)
        }
      }
    })

    // 观察元素
    resizeObserver.observe(el)

    // 将resizeObserver实例存储在元素上，以便以后可以取消观察
    el._resizeObserver = resizeObserver
  },
  beforeUnmount(el) {
    // 在元素销毁之前，取消观察
    if (el._resizeObserver) {
      el._resizeObserver.disconnect()
      delete el._resizeObserver
    }
  },
}

export default directive
