<script lang="jsx">
  import { casLogin, getUser } from '@/api'
  import { onMounted, defineComponent, reactive } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { useUserStore } from '@/store'
  import { unmountLoading } from '@/core/loading'
  import FingerprintJS from '@fingerprintjs/fingerprintjs'

  export default defineComponent({
    name: 'Login',
    setup() {
      const route = useRoute()
      const router = useRouter()

      const userStore = useUserStore()

      // userStore.$patch({ token: '8c5eec269e3c450faed58c28b063e0e4' })

      const state = reactive({
        isShow: false,
        errorInfo: {},
        fingerprint: '',
      })

      onMounted(() => {
        setTimeout(() => {
          createFingerprint()
        }, 500)
      })

      function casLoginFn() {
        if (window.location && !window.location.search) {
          // 认证平台跳回，并在地址栏包含ticket,获取ticket
          window.location.href = `${import.meta.env.VITE_CAS_URL}/login?service=${window.location.origin}`
        } else {
          // 单点登录url中没有ticket，跳至认证中心url
          let service = window.location.origin
          // debugger
          let ticket = route.query?.ticket
          if (ticket) {
            casLogin({
              appId: import.meta.env.VITE_APPID,
              service,
              ticket,
              fingerprint: state.fingerprint,
            })
              .then(res => loginSuccess(res))
              .catch(err => requestFailed(err))
              .finally(() => {})
          } else {
            //没有cas登录、没有ticket直接返回登录页
            window.location.href = `${import.meta.env.VITE_CAS_URL}/login?service=${window.location.origin}`
          }
        }
      }

      function onBackLogin() {
        userStore.$patch({ token: null })

        window.location.href = `${import.meta.env.VITE_CAS_URL}/login?service=${window.location.origin}`
      }

      //登录成功
      async function loginSuccess(res) {
        userStore.$patch({ user: res.data.user })
        userStore.$patch({ token: res.data.token })

        unmountLoading()

        router.push({ path: route.query?.redirect || '/' })
      }
      //登录失败
      function requestFailed(err) {
        if (userStore.serveErr) {
          state.errorInfo = userStore.serveErr
        } else {
          state.errorInfo = err.date
        }

        unmountLoading()

        state.isShow = true
      }

      // 创建浏览器指纹
      async function createFingerprint() {
        // 初始化FingerprintJS
        const fp = await FingerprintJS.load()
        // 获取访问者的指纹
        const result = await fp.get()

        state.fingerprint = result.visitorId

        nextTick(() => {
          casLoginFn()
        })
      }

      return () =>
        state.isShow && (
          <div className='login-container'>
            <div className='login-box'>
              <n-result status='warning' title='单点登录失败' description={`${state.errorInfo.code}: ${state.errorInfo.message}`}>
                {{
                  footer: () => (
                    <n-button type='primary' onClick={onBackLogin}>
                      返回登录页
                    </n-button>
                  ),
                }}
              </n-result>
            </div>
          </div>
        )
    },
  })
</script>

<style lang="scss" scoped>
  .login-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .login-box {
  }
</style>
