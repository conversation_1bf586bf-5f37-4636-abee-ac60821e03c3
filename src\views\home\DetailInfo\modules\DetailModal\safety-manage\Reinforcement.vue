<template>
  <div style="height: 100%">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="除险加固名称">
        <a-input v-model="queryParam.riskName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>

      <!-- <a-form-item label="培训时间">
        <a-range-picker
          allow-clear
          format="YYYY-MM-DD"
          valueFormat="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          v-model="queryParam.trainingTime"
          :disabled-date="disabledDate"
          @openChange="() => (disabledDealDate = null)"
          @change="onRangeChange"
          @calendarChange="calendarChange"
        />
      </a-form-item> -->

      <a-form-item label="状态">
        <a-select allowClear v-model="queryParam.riskStatus" placeholder="请选择">
          <a-select-option v-for="item in riskStatusOptions" :key="item.key" :value="item.key">
            {{ item.value }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          @refresh="getList"
          @selectChange="selectChange"
          @sortChange="sortChange"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button>
          </div>
        </VxeTable>

        <ReinforcementDetailModal
          v-if="showDetailModal"
          :riskStatusOptions="riskStatusOptions"
          ref="detailModalRef"
          @close="showDetailModal = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import moment from 'moment'
  import { getRiskPage, deleteRisk } from './services'
  import { getOptions, getProjectTree } from '@/api/common'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import ReinforcementDetailModal from './modules/ReinforcementDetailModal.vue'

  export default {
    name: 'Reinforcement',
    components: {
      VxeTable,
      VxeTableForm,
      ReinforcementDetailModal
    },
    props: {
      projectId: {}
    },
    data() {
      return {
        showFormModal: false,
        showDetailModal: false,
        riskStatusOptions: [],
        projectOptions: [],

        list: [],
        tableTitle: '除险加固',
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,

        disabledDealDate: null,
        queryParam: {
          pageNum: 1,
          pageSize: 10,
          projectId: this.projectId,
          riskName: '',
          riskStatus: undefined,
          sort: []
        },
        columns: [
          { type: 'checkbox', width: 30, },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '除险加固名称',
            field: 'riskName',
            minWidth: 140
          },
          {
            title: '开始时间',
            field: 'startTime',
            minWidth: 140,
            sortable: true
          },
          {
            title: '设计单位',
            field: 'designUnit',
            minWidth: 140
          },
          {
            title: '施工单位',
            field: 'constructionUnit',
            minWidth: 140
          },
          {
            title: '验收单位',
            field: 'acceptanceUnit',
            minWidth: 140
          },
          {
            title: '管理单位',
            field: 'unitManagement',
            minWidth: 140
          },
          {
            title: '状态',
            field: 'riskStatus',
            minWidth: 120,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <div class='common-status-box'>
                    <i
                      class={[
                        'common-status-icon',
                        row.riskStatus == '4' ? 'common-status-completed' : 'common-status-waiting',
                      ]}
                    ></i>
                    <span>{this.riskStatusOptions.find(el => el.key == row.riskStatus).value}</span>
                  </div>
                )
              }
            }
          },
          {
            title: '结束时间',
            field: 'endTime',
            minWidth: 140,
            sortable: true
          },
          {
            title: '所属工程',
            field: 'projectName',
            showOverflow: 'tooltip',
            minWidth: 100
          },
          {
            title: '操作',
            field: 'operate',
            width: 140,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetail(row)}>查看</a>
                  </span>
                )
              }
            }
          }
        ]
      }
    },
    computed: {},
    watch: {},
    created() {
      // 获取工程树
      getProjectTree({ objectCategoryId: this.queryParam.treeNodeId }).then(res => {
        this.projectOptions = res.data
      })
      getOptions('riskStatus').then(res => {
        this.riskStatusOptions = res?.data || []
      })
      this.getList()
    },
    methods: {
      disabledDate(current) {
        let start = moment(this.disabledDealDate).subtract(7, 'd')
        let end = moment(this.disabledDealDate).add(7, 'd')
        return current > end || current < start
      },
      calendarChange(dates) {
        if (dates?.length == 1) {
          this.disabledDealDate = dates[0]
        }
      },
      onRangeChange(dates) {
        this.queryParam.startTime = dates[0]
        this.queryParam.endTime = dates[1]
      },

      /** 查询列表 */
      getList() {
        this.showFormModal = false
        this.loading = true
        this.selectChange({ records: [] })
        getRiskPage(this.queryParam).then(response => {
          this.list = response.data.data
          this.total = response.data.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          pageNum: 1,
          pageSize: 10,
          projectId: this.projectId,
          riskName: '',
          riskStatus: undefined,
          sort: []
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.riskId)
        this.names = valObj.records.map(item => item.riskName)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },

      // 详情
      handleDetail(row) {
        this.showDetailModal = true
        this.$nextTick(() => this.$refs.detailModalRef.handleDetail(row))
      },

      // 导出
      handleExport() {},

      // 操作完成后
      onOperationComplete() {
        this.getList()
      }
    }
  }
</script>
<style lang="scss" scoped></style>
