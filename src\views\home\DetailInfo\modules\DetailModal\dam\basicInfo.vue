<template>
  <div class="basic-info">
    <div class="content">
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        layout="horizontal"
        :labelCol="{ span: 12 }"
        :wrapperCol="{ span: 12 }"
        labelAlign="right"
      >
        <a-row :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">
              基本信息
              <a-button style="margin-left: 10px" type="primary" @click="onBtnClick" :loading="loading">
                {{ this.type == 'detail' ? '编辑' : '确定' }}
              </a-button>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">水库大坝代码：</span>
              <span class="value">{{ data?.projectCode }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">水库大坝名称：</span>
              <span class="value">{{ data?.projectName }}</span>
            </div>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="行政区划">
              <div class="form-value">{{ data.districtFullName }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="水库大坝所在位置" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <div class="form-value">{{ data.location }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="起点经度(°)">
              <a-input-number v-if="type == 'edit'" v-model="form.longitude" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.longitude }}</div>
              <!-- <div class="form-value">{{ data.longitude }}</div> -->
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="起点维度(°)">
              <a-input-number v-if="type == 'edit'" v-model="form.latitude" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.latitude }}</div>
              <!-- <div class="form-value">{{ data.latitude }}</div> -->
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="终点经度(°)">
              <a-input-number v-if="type == 'edit'" v-model="form.longitude" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.longitude }}</div>
              <!-- <div class="form-value">{{ data.longitude }}</div> -->
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="终点维度(°)">
              <a-input-number v-if="type == 'edit'" v-model="form.latitude" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.latitude }}</div>
              <!-- <div class="form-value">{{ data.latitude }}</div> -->
            </a-form-model-item>
          </a-col>

          <!-- <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="大坝所在位置">
              <div class="form-value">{{ data.location }}</div>
            </a-form-model-item>
          </a-col> -->

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title second-title">主要特征信息</div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="是否主坝">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.ifMainDam"
                placeholder="请选择"
                :options="ifMainDamOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">{{ ifMainDamOptions.find(el => el.value == data.ifMainDam)?.label }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="工程等别">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.engGrad"
                placeholder="请选择"
                :options="engGradOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">{{ engGradOptions.find(el => el.value == data.engGrad)?.label }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="大坝级别">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.damGrad"
                placeholder="请选择"
                :options="damGradOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">{{ damGradOptions.find(el => el.value == data.damGrad)?.label }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="最大坝高(m)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.damMaxHeig"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.damMaxHeig }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="坝顶长度(m)">
              <a-input-number v-if="type == 'edit'" v-model="form.damTopLen" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.damTopLen }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="坝顶宽度(m)">
              <a-input-number v-if="type == 'edit'" v-model="form.damTopWid" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.damTopWid }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="高程系统">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.elevSys"
                placeholder="请选择"
                :options="elevSysOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">{{ elevSysOptions.find(el => el.value == data.elevSys)?.label }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="坝顶高程(m)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.damTopElev"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.damTopElev }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="大坝材料类型">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.damTypeMat"
                placeholder="请选择"
                :options="damTypeMatOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ damTypeMatOptions.find(el => el.value == data.damTypeMat)?.label }}
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="大坝结构类型">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.damTypeStr"
                placeholder="请选择"
                :options="damTypeStrOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ damTypeStrOptions.find(el => el.value == data.damTypeStr)?.label }}
              </div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="工程建设情况">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.elevStat"
                placeholder="请选择"
                :options="elevStatOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ elevStatOptions?.find(el => el.value == data.elevStat)?.label }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="开工时间" prop="startDate">
              <a-date-picker
                v-if="type == 'edit'"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                v-model="form.startDate"
                placeholder="请选择"
                style="width: 100%"
              />
              <div v-else class="form-value">{{ data.startDate }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="建成时间" prop="compDate">
              <a-date-picker
                v-if="type == 'edit'"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                v-model="form.compDate"
                placeholder="请选择"
                style="width: 100%"
              />
              <div v-else class="form-value">{{ data.compDate }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="备注" prop="note" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <a-textarea v-if="type == 'edit'" class="area-text" v-model="form.note" placeholder="请输入" allowClear />
              <div v-else class="form-value">{{ data.note }}</div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
  </div>
</template>

<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getDam, updateDam } from './services'
  import { updateListFromStr } from '@/utils/util.js'
  export default {
    name: 'BasicInfo',
    components: {},
    props: {
      projectId: {},
    },
    data() {
      return {
        loading: false,
        data: {},

        ifMainDamOptions: [], //是否主坝
        damGradOptions: [], //坝等级
        elevSysOptions: [], //高程系统
        damTypeMatOptions: [], //大坝材料类型
        damTypeStrOptions: [], //大坝结构类型

        engGradOptions: [], //工程等别
        elevStatOptions: [], //工程建设情况

        type: 'detail',
        form: {
          compDate: '',
          damGrad: '',
          damMaxHeig: undefined,
          damTopElev: undefined,
          damTopLen: undefined,
          damTopWid: undefined,
          damTypeMat: '',
          damTypeStr: '',
          elevStat: '',
          elevSys: '',
          engGrad: '',
          id: undefined,
          ifMainDam: '',
          lowLeftLat: '',
          lowLeftLong: '',
          note: '',
          projectId: undefined,
          startDate: '',
          upRightLat: '',
          upRightLong: '',
        },
        rules: {},
      }
    },
    created() {
      this.init()
      this.getDataSource()
    },
    methods: {
      init() {
        getOptions('ifMainDam').then(res => {
          this.ifMainDamOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('damGrad').then(res => {
          this.damGradOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })

        getOptions('elevationSystem').then(res => {
          this.elevSysOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('damTypeMat').then(res => {
          this.damTypeMatOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('damTypeStr').then(res => {
          this.damTypeStrOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })

        //工程等别
        getOptions('projectWait').then(res => {
          this.engGradOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        // 工程建设情况-engStat
        getOptions('engStat').then(res => {
          this.elevStatOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
      },
      getDataSource() {
        getDam({ projectId: this.projectId }).then(res => {
          this.data = res.data
          this.form = {
            ...res.data,
            projectId: this.projectId,
            ifMainDam: res.data.ifMainDam ? `${res.data.ifMainDam}` : undefined,
            engGrad: res.data.engGrad ? `${res.data.engGrad}` : undefined,

            elevSys: res.data.elevSys ? `${res.data.elevSys}` : undefined,
            damGrad: res.data.damGrad ? `${res.data.damGrad}` : undefined,
            damTypeMat: res.data.damTypeMat ? `${res.data.damTypeMat}` : undefined,
            elevStat: res.data.elevStat ? `${res.data.elevStat}` : undefined,
            damTypeStr: res.data.damTypeStr ? `${res.data.damTypeStr}` : undefined,
          }
        })
      },

      onBtnClick() {
        if (this.type == 'edit') {
          this.form.projectId = this.projectId
          const params = { ...this.form }

          updateDam(params)
            .then(res => {
              this.$message.success('修改成功', 3)
              this.getDataSource()
            })
            .finally(() => (this.loading = false))
        }

        this.type = this.type == 'detail' ? 'edit' : 'detail'
      },
    },
  }
</script>

<style lang="less" scoped>
  @import url('~@/global.less');

  @import url('~@/assets/styles/basic-info.less');
</style>
