@use '@/assets/fonts/fonts.scss';
@use 'variables.scss';
@use 'custom.scss';

*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  width: 100%;
  height: 100%;
  overflow: hidden;
  color: $text-main;
  font-family:
    SourceHanSansCN-Normal,
    v-sans,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    sans-serif,
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Segoe UI Symbol';
  line-height: 1.1;
}

#app {
  width: 100%;
  height: 100%;
}

a {
  text-decoration: none;
  color: inherit;
}

a:hover,
a:link,
a:visited,
a:active {
  text-decoration: none;
}

.text-overflow1 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.text-overflow2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

/* 谷歌美化滚动条 */
*::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  inset: 3px 4px 2px auto;
}
*::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.11);
  background-color: transparent;
  border-radius: 10px;
}
*::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: rgba(0, 0, 0, 0.25);
}
*::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.4);
  cursor: pointer;
}
*::-webkit-scrollbar-corner {
  background: transparent;
}
