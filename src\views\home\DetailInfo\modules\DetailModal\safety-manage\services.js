import request from '@/utils/request'

// 安全鉴定-列表分页查询
export function getSafetyPage(data) {
  return request({
    url: '/prjstd/safety/page',
    method: 'post',
    data
  })
}

// 新增
export function addSafety(data) {
  return request({
    url: '/prjstd/safety/add',
    method: 'post',
    data
  })
}

// 删除
export function deleteSafety(params) {
  return request({
    url: '/prjstd/safety/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 详情
export function getSafety(params) {
  return request({
    url: '/prjstd/safety/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 修改
export function updateSafety(data) {
  return request({
    url: '/prjstd/safety/update',
    method: 'post',
    data
  })
}

// 除险加固-列表分页查询
export function getRiskPage(data) {
  return request({
    url: '/prjstd/risk/page',
    method: 'post',
    data
  })
}

// 新增
export function addRisk(data) {
  return request({
    url: '/prjstd/risk/add',
    method: 'post',
    data
  })
}

// 删除
export function deleteRisk(params) {
  return request({
    url: '/prjstd/risk/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 详情
export function getRisk(params) {
  return request({
    url: '/prjstd/risk/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 修改
export function updateRisk(data) {
  return request({
    url: '/prjstd/risk/update',
    method: 'post',
    data
  })
}

// 注册备案-列表分页查询
export function getFilingPage(data) {
  return request({
    url: '/prjstd/filing/page',
    method: 'post',
    data
  })
}

// 新增
export function addFiling(data) {
  return request({
    url: '/prjstd/filing/add',
    method: 'post',
    data
  })
}

// 删除
export function deleteFiling(params) {
  return request({
    url: '/prjstd/filing/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 详情
export function getFiling(params) {
  return request({
    url: '/prjstd/filing/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 修改
export function updateFiling(data) {
  return request({
    url: '/prjstd/filing/update',
    method: 'post',
    data
  })
}

// 安全管理_降等报废-列表分页查询
export function getScrapPage(data) {
  return request({
    url: '/prjstd/scrap/page',
    method: 'post',
    data
  })
}

// 新增
export function addScrap(data) {
  return request({
    url: '/prjstd/scrap/add',
    method: 'post',
    data
  })
}

// 删除
export function deleteScrap(params) {
  return request({
    url: '/prjstd/scrap/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 详情
export function getScrap(params) {
  return request({
    url: '/prjstd/scrap/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 修改
export function updateScrap(data) {
  return request({
    url: '/prjstd/scrap/update',
    method: 'post',
    data
  })
}
