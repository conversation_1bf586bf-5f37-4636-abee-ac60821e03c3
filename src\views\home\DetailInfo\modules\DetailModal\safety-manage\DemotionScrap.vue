<template>
  <div style="height: 100%">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="名称">
        <a-input v-model="queryParam.scrapName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>

      <a-form-item label="状态">
        <a-select allowClear v-model="queryParam.scrapStatus" placeholder="请选择">
          <a-select-option v-for="item in scrapStatusOptions" :key="item.key" :value="item.key">
            {{ item.value }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="开始时间">
        <a-range-picker
          allow-clear
          format="YYYY-MM-DD"
          valueFormat="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          v-model="queryParam.scrapTime"
          :disabled-date="disabledDate"
          @openChange="() => (disabledDealDate = null)"
          @change="onRangeChange"
          @calendarChange="calendarChange"
        />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          @refresh="getList"
          @selectChange="selectChange"
          @sortChange="sortChange"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button>
          </div>
        </VxeTable>

        <DemotionScrapDetailModal
          v-if="showDetailModal"
          :scrapStatusOptions="scrapStatusOptions"
          ref="detailModalRef"
          @close="showDetailModal = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import moment from 'moment'
  import { getScrapPage, deleteScrap } from './services'
  import { getOptions, getProjectTree } from '@/api/common'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import DemotionScrapDetailModal from './modules/DemotionScrapDetailModal.vue'

  export default {
    name: 'DemotionScrap',
    components: {
      VxeTable,
      VxeTableForm,
      DemotionScrapDetailModal
    },
    props: {
      projectId: {}
    },
    data() {
      return {
        showFormModal: false,
        showDetailModal: false,
        scrapStatusOptions: [],
        projectOptions: [],

        list: [],
        tableTitle: '降等报废',
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,

        disabledDealDate: null,
        queryParam: {
          endTime: '',
          pageNum: 1,
          pageSize: 10,
          projectId: this.projectId,
          scrapName: '',
          scrapStatus: undefined,
          sort: [],
          startTime: ''
        },
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '名称',
            field: 'scrapName',
            minWidth: 140
          },
          {
            title: '管理单位',
            field: 'unitManagement',
            minWidth: 140
          },
          {
            title: '开始时间',
            field: 'startTime',
            minWidth: 140,
            sortable: true
          },
          {
            title: '状态',
            field: 'scrapStatus',
            minWidth: 120,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                <div class='common-status-box'>
                  <i
                    class={[
                      'common-status-icon',
                      row.scrapStatus == '3' ? 'common-status-completed' : 'common-status-waiting',
                    ]}
                  ></i>
                  <span>{this.scrapStatusOptions.find(el => el.key == row.scrapStatus).value}</span>
                </div>
              )
              }
            }
          },
          // {
          //   title: '结束时间',
          //   field: 'endTime',
          //   minWidth: 140,
          //   sortable: true
          // },
          {
            title: '所属工程',
            field: 'projectName',
            minWidth: 100
          },
          {
            title: '操作',
            field: 'operate',
            width: 140,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetail(row)}>查看</a>
                  </span>
                )
              }
            }
          }
        ]
      }
    },
    computed: {},
    watch: {},
    created() {
      // 获取工程树
      getProjectTree({ objectCategoryId: this.queryParam.treeNodeId }).then(res => {
        this.projectOptions = res.data
      })

      getOptions('scrapStatus').then(res => {
        this.scrapStatusOptions = res?.data || []
      })
      this.getList()
    },
    methods: {
      disabledDate(current) {
        let start = moment(this.disabledDealDate).subtract(7, 'd')
        let end = moment(this.disabledDealDate).add(7, 'd')
        return current > end || current < start
      },
      calendarChange(dates) {
        if (dates?.length == 1) {
          this.disabledDealDate = dates[0]
        }
      },
      onRangeChange(dates) {
        this.queryParam.startTime = dates[0]
        this.queryParam.endTime = dates[1]
      },

      /** 查询列表 */
      getList() {
        this.showFormModal = false
        this.loading = true
        this.selectChange({ records: [] })
        getScrapPage(this.queryParam).then(response => {
          this.list = response.data.data
          this.total = response.data.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          endTime: '',
          pageNum: 1,
          pageSize: 10,
          projectId: this.projectId,
          scrapName: '',
          scrapStatus: undefined,
          sort: [],
          startTime: ''
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.scrapId)
        this.names = valObj.records.map(item => item.scrapName)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },

      // 导出
      handleExport() {},
      // 查看
      handleDetail(row) {
        this.showDetailModal = true
        this.$nextTick(() => this.$refs.detailModalRef.handleDetail(row))
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      }
    }
  }
</script>
<style lang="less" scoped></style>
