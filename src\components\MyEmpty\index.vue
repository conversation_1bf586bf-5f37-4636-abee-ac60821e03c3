<template><component :is="render()"></component></template>

<script setup lang="tsx" name="MyEmpty">
  const attrs = useAttrs()
  const render = () => (
    <n-empty {...attrs}>
      {{
        icon: () => <img src={attrs.src} />,
      }}
    </n-empty>
  )
</script>
<style lang="scss" scoped>
  :deep(.n-empty__icon) {
    // max-width: 100%; /* 图片宽度自适应容器 */
    // height: auto; /* 图片高度根据比例自动调整 */
    width: 1.19rem;
    height: 0.92rem;
    > img {
      width: 1.19rem;
      height: 0.92rem;
    }
  }
</style>
