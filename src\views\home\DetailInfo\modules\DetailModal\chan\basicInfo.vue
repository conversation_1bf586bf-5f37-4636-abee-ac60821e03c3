<template>
  <div class="basic-info">
    <div class="content">
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        layout="horizontal"
        :labelCol="{ span: 12 }"
        :wrapperCol="{ span: 12 }"
        labelAlign="right"
      >
        <a-row :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">
              基本信息
              <a-button style="margin-left: 10px" type="primary" @click="onBtnClick" :loading="loading">
                {{ this.type == 'detail' ? '编辑' : '确定' }}
              </a-button>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">渠(沟)道代码：</span>
              <span class="value">{{ data?.projectCode }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">渠(沟)道名称：</span>
              <span class="value">{{ data?.projectName }}</span>
            </div>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="行政区划">
              <div class="form-value">{{ data.districtFullName }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :span="24">
            <a-form-model-item
              label="渠(沟)道所在位置"
              :labelCol="{ span: 5 }"
              :wrapperCol="{ span: 11 }"
              class="location"
            >
              <div class="form-value">{{ data.location }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="起点经度(°)">
              <a-input-number v-if="type == 'edit'" v-model="form.startLong" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.startLong }}</div>
              <!-- <div class="form-value">{{ data.longitude }}</div> -->
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="起点维度(°)">
              <a-input-number v-if="type == 'edit'" v-model="form.startLat" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.startLat }}</div>
              <!-- <div class="form-value">{{ data.latitude }}</div> -->
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="终点经度(°)">
              <a-input-number v-if="type == 'edit'" v-model="form.endLong" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.endLong }}</div>
              <!-- <div class="form-value">{{ data.longitude }}</div> -->
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="终点维度(°)">
              <a-input-number v-if="type == 'edit'" v-model="form.endLat" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.endLat }}</div>
              <!-- <div class="form-value">{{ data.latitude }}</div> -->
            </a-form-model-item>
          </a-col>

          <!-- <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="渠(沟)道所在位置">
              <div class="form-value">{{ data.location }}</div>
            </a-form-model-item>
          </a-col> -->

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title second-title">主要特征信息</div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="渠(沟)道类别">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.chanType"
                placeholder="请选择"
                :options="chanTypeOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ chanTypeOptions.find(el => el.value == data.chanType)?.label }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="渠(沟)道长度(km)">
              <a-input-number v-if="type == 'edit'" v-model="form.chanLen" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.chanLen }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="设计流量(m³/s)">
              <a-input-number v-if="type == 'edit'" v-model="form.decFlow" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.decFlow }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="备注" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <a-textarea v-if="type == 'edit'" class="area-text" v-model="form.note" placeholder="请输入" allowClear />
              <div v-else class="form-value">{{ data.note }}</div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
  </div>
</template>

<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getChan, updateChan } from './services'
  export default {
    name: 'BasicInfo',
    components: {},
    props: {
      projectId: {},
    },
    data() {
      return {
        loading: false,
        data: {},

        chanTypeOptions: [], //渠 (沟)道类别

        type: 'detail',
        form: {
          chanLen: undefined,
          chanType: '',
          decFlow: undefined,
          endLat: '',
          endLong: '',
          id: undefined,
          note: '',
          projectId: undefined,
          startLat: '',
          startLong: '',
        },
        rules: {},
      }
    },
    created() {
      this.init()
      this.getDataSource()
    },
    methods: {
      init() {
        getOptions('chanType').then(res => {
          this.chanTypeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
      },
      getDataSource() {
        getChan({ projectId: this.projectId }).then(res => {
          this.data = res.data
          this.form = {
            ...res.data,
            projectId: this.projectId,
            chanType: res.data.chanType ? res.data.chanType : undefined,
            
          }
        })
      },

      onBtnClick() {
        if (this.type == 'edit') {
          this.form.projectId = this.projectId
          const params = { ...this.form }

          updateChan(params)
            .then(res => {
              this.$message.success('修改成功', 3)
              this.getDataSource()
            })
            .finally(() => (this.loading = false))
        }

        this.type = this.type == 'detail' ? 'edit' : 'detail'
      },
    },
  }
</script>

<style lang="scss" scoped>
 

  @import url('~@/assets/styles/basic-info.scss');
</style>
