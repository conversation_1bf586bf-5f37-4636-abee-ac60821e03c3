<template>
  <div class="h-full w-full reactive">
    <Left
      v-show="isShow.isShowLeft"
      :isShow="isShow"
      :tabLevelOptions1="tabLevelOptions1"
      v-model:activeTabLevel2="activeTabLevel2"
      :allData="allData"
      v-model:activeItem="activeItem"
      :isDataMode="isDataMode"
      v-model:isExpand="isExpand"
    />

    <AreaAndToolControl
      v-model="isShow"
      v-model:currentDistrict="currentDistrict"
      v-model:isDataMode="isDataMode"
      :isExpand="isExpand"
    />

    <Right
      v-show="isShow.isShowRight"
      :activeTabLevel2="activeTabLevel2"
      :tabLevelOptions1="tabLevelOptions1"
      v-model:allData="allData"
    />

    <Map
      :isShow="isShow"
      :allData="allData"
      v-model:activeItem="activeItem"
      v-model:activeTabLevel2="activeTabLevel2"
      :currentDistrict="currentDistrict"
      :isDataMode="isDataMode"
    />

    <DetailInfo v-model:activeItem="activeItem" />
  </div>
</template>

<script setup lang="tsx" name="Home">
  import Map from './Map'
  import Left from './Left'
  import AreaAndToolControl from './AreaAndToolControl'
  import Right from './Right'
  import DetailInfo from './DetailInfo'

  import { objectCategoryFirstLevelList } from './services'

  const isShow = $ref({
    isShowLeft: true,
    isShowRight: true,
    isShowTool: true,
    isShowLabel: false,
  })

  let activeTabLevel2 = $ref(null)
  //objectCategoryCode
  let activeTabLevel1 = $ref(null)

  let tabLevelOptions1 = $ref([])

  // 所有数据--右侧选中变化
  let allData = $ref(null)

  let activeItem = $ref(null)

  let currentDistrict = $ref(null)

  // 是否打开数据模式
  let isDataMode = $ref(false)
  let isExpand = $ref(false)

  watch(
    () => isDataMode,
    newVal => {
      if (newVal === true) {
        isExpand = true
      }
      if (newVal === false) {
        isExpand = false
      }
    },
  )

  objectCategoryFirstLevelList().then((res: any) => {
    tabLevelOptions1 = res.data || []
  })
</script>

<style lang="scss" scoped></style>
