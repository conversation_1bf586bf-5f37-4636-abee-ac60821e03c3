{
  "compilerOptions": {
    "target": "ESNext", // 将代码编译为最新版本的 JS
    "lib": ["ESNext", "DOM", "DOM.Iterable"], // 引入 ES 最新特性和 DOM 接口的类型定义
    "module": "ESNext", // 使用 ES Module 格式打包编译后的文件
    "skipLibCheck": true, // 跳过对 .d.ts 文件的类型检查
    "baseUrl": ".", //查询的基础路径
    "paths": { "@/*": ["src/*"], "~/*": ["./*"] }, //路径映射,配合别名使用
    "types": ["node", "vite/client", "unplugin-icons/types/vue", "@vue-macros/reactivity-transform/macros-global"],

    /* Node mode */
    "moduleResolution": "node", // 使用 Node 的模块解析策略
    "allowImportingTsExtensions": true, //允许在模块导入语句中使用Typescript文件的扩展名（.ts）
    "allowSyntheticDefaultImports": true, //允许使用默认导入语句
    "allowJs": true, //允许使用js
    "resolveJsonModule": true, // 允许引入 JSON 文件
    "isolatedModules": true, // 要求所有文件都是 ES Module 模块。
    "noEmit": true, // 不输出文件,即编译后不会生成任何js文件
    "jsx": "preserve", // 保留原始的 JSX 代码，不进行编译
    "jsxFactory": "h",
    "jsxImportSource": "vue",
    "jsxFragmentFactory": "React.Fragment",
    "esModuleInterop": true, // 允许使用 import 引入使用 export 导出
    "forceConsistentCasingInFileNames": true, // 确保导入时的大小写写法正确。

    "declaration": true, // 生成对应的 .d.ts 声明文件。
    "declarationMap": true // 为 .d.ts 文件生成 source map 文件。
  },
  //需要检测的文件
  "include": ["src/*.ts", "src/*.vue", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "@types/**/*.d.ts"],
  "exclude": ["node_modules", "dist"]
}
