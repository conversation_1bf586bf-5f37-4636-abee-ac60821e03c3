// 简单的测试文件，用于验证 overlay 管理器
import overlayManager from './overlayManager.js'

// 模拟 Mapbox 地图实例
const mockMapInstance = {
  addControl: jest.fn(),
  removeControl: jest.fn()
}

// 模拟 Deck.gl 图层
const mockLayer1 = { id: 'test-layer-1' }
const mockLayer2 = { id: 'test-layer-2' }

describe('OverlayManager', () => {
  beforeEach(() => {
    // 重置管理器状态
    overlayManager.destroy()
  })

  test('should initialize overlay correctly', () => {
    const overlay = overlayManager.init(mockMapInstance)
    
    expect(overlay).toBeDefined()
    expect(mockMapInstance.addControl).toHaveBeenCalledWith(overlay)
  })

  test('should add and remove layers correctly', () => {
    overlayManager.init(mockMapInstance)
    
    // 添加图层
    overlayManager.addLayer('layer1', mockLayer1)
    overlayManager.addLayer('layer2', mockLayer2)
    
    expect(overlayManager.hasLayer('layer1')).toBe(true)
    expect(overlayManager.hasLayer('layer2')).toBe(true)
    expect(overlayManager.getLayerCount()).toBe(2)
    
    // 移除图层
    overlayManager.removeLayer('layer1')
    
    expect(overlayManager.hasLayer('layer1')).toBe(false)
    expect(overlayManager.hasLayer('layer2')).toBe(true)
    expect(overlayManager.getLayerCount()).toBe(1)
  })

  test('should clear all layers', () => {
    overlayManager.init(mockMapInstance)
    
    overlayManager.addLayer('layer1', mockLayer1)
    overlayManager.addLayer('layer2', mockLayer2)
    
    overlayManager.clearAllLayers()
    
    expect(overlayManager.getLayerCount()).toBe(0)
  })

  test('should destroy correctly', () => {
    const overlay = overlayManager.init(mockMapInstance)
    
    overlayManager.addLayer('layer1', mockLayer1)
    overlayManager.destroy()
    
    expect(mockMapInstance.removeControl).toHaveBeenCalledWith(overlay)
    expect(overlayManager.getLayerCount()).toBe(0)
  })
})
