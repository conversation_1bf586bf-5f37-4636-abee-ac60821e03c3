<script setup lang="ts" name="MyIcon">
  // import { Icon } from '@iconify/vue'

  import { isExternal } from '@/utils'

  interface Props {
    name?: string
  }
  const props = withDefaults(defineProps<Props>(), {
    name: '',
  })

  const attrs = useAttrs()

  // 获取assets/svgs所有.svg文件
  const meSvgFiles = import.meta.glob('@/assets/svgs/**/*.svg')
  const isMeIcon = computed(() => {
    if (!props.name) return false

    return Object.keys(meSvgFiles).some(key => props.name === key.slice(key.lastIndexOf('/') + 1, key.lastIndexOf('.')))
  })

  // 外链 icon
  const isExternalIcon = computed(() => isExternal(props.name))
  const styleExternalIcon = computed(() => {
    return {
      mask: `url(${props.name}) no-repeat 50% 50%`,
      '-webkit-mask': `url(${props.name}) no-repeat 50% 50%`,
    }
  })
</script>

<template>
  <!-- 外链icon -->
  <div v-if="isExternalIcon" :style="styleExternalIcon" class="svg-external-icon svg-icon" v-bind="attrs" />

  <!-- iconify离线 给class-->
  <i v-if="!props.name && !isMeIcon" v-bind="attrs"></i>

  <!-- 本地图标 -->
  <!-- <i v-if="isMeIcon" :class="`i-me:${props.name}`" v-bind="attrs" /> -->
  <svg v-if="isMeIcon" class="svg-icon" aria-hidden="true" v-bind="attrs">
    <use :xlink:href="`#icon-${props.name}`" />
  </svg>

  <!-- iconify在线 -->
  <!-- <Icon v-if="props.name && !isMeIcon" :icon="props.name" v-bind="attrs" /> -->
</template>

<style scoped>
  .svg-icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
  }

  .svg-external-icon {
    background-color: currentColor;
    mask-size: cover !important;
    display: inline-block;
  }
</style>
