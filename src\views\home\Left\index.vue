<template>
  <div class="panel" :style="{ width: isExpand ? '6.11rem' : '3.94rem' }">
    <div class="header bg-#E8F3FF pl-0.16rem select-none">
      <n-tabs animated size="small" type="bar" v-model:value="state.tabLevel1" @update:value="changeTabLevel1">
        <n-tab v-for="el in attrs.tabLevelOptions1" :name="el.objectCategoryId" :tab="el.objectCategoryName"></n-tab>
      </n-tabs>
    </div>

    <div class="flex-1 flex overflow-hidden">
      <div class="w-0.64rem h-full bg-#F7F8FA select-none">
        <n-tabs
          :key="state.tabKey"
          animated
          size="small"
          type="bar"
          placement="left"
          v-model:value="state.tabLevel2"
          @update:value="changeTabLevel2"
        >
          <n-tab v-for="el in state.tabLevelOptions2" :name="el.objectCategoryCode">
            <div v-if="el.objectCategoryName.length <= 4" flex-col items-center>
              <div
                class="size-[0.24rem]"
                :style="{
                  background: `url(${getImageUrl(`leftLevel2/${el.objectCategoryCode}.png`)}) no-repeat center / 100% 100%`,
                }"
              ></div>
              <div class="mt-0.12rem">{{ el.objectCategoryName }}</div>
            </div>

            <n-tooltip v-else placement="right" trigger="hover">
              <template #trigger>
                <div class="flex-col items-center">
                  <div
                    class="size-[0.24rem]"
                    :style="{
                      background: `url(${getImageUrl(`leftLevel2/${el.objectCategoryCode}.png`)}) no-repeat center / 100% 100%`,
                    }"
                  ></div>
                  <div class="mt-0.12rem">{{ el.objectCategoryName.slice(0, 4) }}</div>
                </div>
              </template>
              <span>{{ el.objectCategoryName }}</span>
            </n-tooltip>
          </n-tab>
        </n-tabs>
      </div>

      <TableList
        :tabLevel1="state.tabLevel1"
        :tabLevel2="state.tabLevel2"
        :selected="state.selected"
        v-bind="attrs"
        :isExpand="isExpand"
      />
    </div>

    <div class="expand" v-if="attrs.isDataMode" @click="isExpand = !isExpand">
      <MyIcon
        name="expand-arrow-right"
        class="text-0.18rem"
        :style="{ transform: `rotate(${isExpand ? 180 : 0}deg)`, transition: 'all 0.3s' }"
      />
    </div>
  </div>
</template>

<script setup lang="tsx" name="Left">
  import { objectCategoryCountByFirstLevel } from '../services.ts'
  import TableList from './tableList.vue'

  const attrs = useAttrs()
  const activeTabLevel2 = defineModel('activeTabLevel2')
  const activeTabLevel1 = defineModel('activeTabLevel1')

  const isExpand = defineModel('isExpand')

  const state = reactive({
    tabKey: 1,

    tabLevel1: null,
    tabLevel2: null,
    tabLevelOptions2: [],
    selected: null,
  })

  watch(
    () => attrs.tabLevelOptions1,
    newVal => {
      changeTabLevel1(newVal?.[0].objectCategoryId)
    },
  )

  watch(
    () => attrs.isDataMode,
    newVal => {
      if (attrs.isShow.isShowLeft) {
        objectCategoryCountByFirstLevel({ objectCategoryId: state.tabLevel1, isDataMode: attrs.isDataMode }).then((res: any) => {
          state.tabLevelOptions2 = res.data || []
          changeTabLevel2(state.tabLevel2)
        })
      }
    },
  )

  const changeTabLevel1 = val => {
    state.tabLevel1 = val

    activeTabLevel1.value = attrs.tabLevelOptions1.find(el => el.objectCategoryId == val)?.objectCategoryCode

    objectCategoryCountByFirstLevel({ objectCategoryId: val, isDataMode: attrs.isDataMode }).then((res: any) => {
      state.tabLevelOptions2 = res.data || []
      state.tabLevel2 = state.tabLevelOptions2?.[0].objectCategoryCode
      changeTabLevel2(state.tabLevel2)
    })

    setTimeout(async () => {
      await nextTick(() => {
        // 解决可以滚动时，两端颜色渐变效果消失问题
        state.tabKey++
      })
    }, 500)
  }

  const changeTabLevel2 = val => {
    state.selected = state.tabLevelOptions2?.find(el => el.objectCategoryCode === val)

    activeTabLevel2.value = {
      leftTabLevel1: state.tabLevel1,
      leftTabLevel1Code: attrs.tabLevelOptions1.find(el => el.objectCategoryId === state.tabLevel1)?.objectCategoryCode,
      leftTabLevel2Index: state.tabLevelOptions2.findIndex(el => el.objectCategoryCode === val),
      ...state.selected,
    }
  }
</script>
<style lang="scss" scoped>
  .panel {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 999;
    height: calc(100% - 0.4rem);
    // width: 3.94rem;
    transition: all 0.3s;
    margin: 0.2rem;
    background-color: #ffffff;
    border-radius: 0.08rem;
    // overflow: hidden;
    box-shadow: 0px 0.04rem 0.1rem 0px rgba(0, 0, 0, 0.1);
    border: 1px solid #f2f3f5;

    display: flex;
    flex-direction: column;

    .expand {
      position: absolute;
      width: 0.3rem;
      height: 0.3rem;
      right: -0.15rem;
      top: 50%;
      transform: translateY(-50%);
      background-color: #ffffff;
      box-shadow: 0px 0.04rem 0.1rem 0px rgba(0, 0, 0, 0.15);
      border-radius: 50%;

      display: flex;
      justify-content: center;
      align-items: center;
      transition: all 0.3s;
      cursor: pointer;

      &:hover {
        box-shadow: 0px 0.04rem 0.1rem 0px rgba(0, 0, 0, 0.25);
        transition: all 0.3s;
      }
    }
  }

  :deep(.n-tabs) {
    .v-x-scroll {
      .n-tabs-tab-pad {
        width: 0.24rem;
        height: 0.48rem;
      }
      .n-tabs-tab {
        font-size: 0.14rem;
      }
      .n-tabs-tab--active .n-tabs-tab__label {
        font-family: SourceHanSansCN-Medium;
        color: $text-main !important;
      }
    }

    .n-tabs-nav--left {
      width: 100%;
      .n-tabs-tab-pad {
        height: 0.12rem;
      }
      .n-tabs-tab {
        font-size: 0.14rem;
        padding: 0;

        .n-tabs-tab__label {
          width: 100%;
          padding: 0.08rem 0;
          display: flex;
          flex-direction: column;
          align-items: center;
        }
      }

      .n-tabs-tab--active .n-tabs-tab__label {
        color: $primary-color;
        background: #ffffff;
      }
      .n-tabs-bar {
        background-color: transparent;
      }
    }
  }
  :deep(.n-tabs--left) {
    height: 100%;
    .n-tabs-nav-scroll-wrapper.n-tabs-nav-scroll-wrapper--shadow-start::before {
      height: 110px;
      box-shadow: unset;
      background: linear-gradient(#ffffff, rgba(255, 255, 255, 0));
      z-index: 2;
    }

    .n-tabs-nav-scroll-wrapper.n-tabs-nav-scroll-wrapper--shadow-end::after {
      height: 110px;
      box-shadow: unset;
      background: linear-gradient(rgba(255, 255, 255, 0), #ffffff);
      z-index: 2;
    }
  }
</style>
