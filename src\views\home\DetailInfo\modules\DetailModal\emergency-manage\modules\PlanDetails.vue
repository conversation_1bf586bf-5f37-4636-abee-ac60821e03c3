<template>
  <ant-modal :visible="open" :modal-title="formTitle" modalWidth="600" @cancel="cancel" modalHeight="500">
    <div slot="content">
      <a-row class="form-row" :gutter="32">
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">预案名称：</label>
            <span class="common-value-text">{{ form.emergencyPlanName }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">预案类型：</label>
            <span class="common-value-text">{{ emergencyPlanTypeFormat(form.emergencyPlanType) }}</span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">批复时间：</label>
            <span class="common-value-text">{{ form.replyTime }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">负责人：</label>
            <span class="common-value-text">{{ form.chargeName }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">所属工程：</label>
            <span class="common-value-text" :title="form.projectName">{{ form.projectName }}</span>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24">
          <div style="margin-bottom: 20px">
            <label class="common-label-text">批文：</label>
            <div
              class="file-item"
              v-for="(el, i) in form.approvalAttaches"
              :key="i"
              @click="() => downLoad(el.attachUrl)"
            >
              <a-icon type="paper-clip" />
              <div class="file-name" style="margin-left: 5px">{{ el.attachName }}</div>
            </div>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24">
          <div>
            <label class="common-label-text">预案文件：</label>
            <div class="file-item" v-for="(el, i) in form.planAttaches" :key="i" @click="() => downLoad(el.attachUrl)">
              <a-icon type="paper-clip" />
              <div class="file-name" style="margin-left: 5px">{{ el.attachName }}</div>
            </div>
          </div>
        </a-col>
      </a-row>

      <!-- <div class="form-item-title">配置模板内容</div> -->
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getPlanById } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import moment from 'moment'

  export default {
    name: 'FormDetails',
    components: { AntModal },
    props: ['projectOptions', 'emergencyTypeOptions'],
    data() {
      return {
        formTitle: '',
        form: {
          approvalAttaches: [],
          chargeName: '',
          emergencyPlanId: null,
          emergencyPlanName: '',
          emergencyPlanType: null,
          planAttaches: [],
          projectId: null,
          replyTime: '',
        },
        open: false,
        rules: {}
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 响应等级格式化
      emergencyPlanTypeFormat(value) {
        if (value) {
          return this.emergencyTypeOptions.find(item => item.key == value)?.value
        } else {
          return ''
        }
      },
      // 年份格式化
      yearFormat(value) {
        if (value) {
          return moment(value).format('YYYY')
        } else {
          return ''
        }
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
          this.planDate = []
          this.taskDate = []
        }
      },
      downLoad(url) {
        window.open(url)
      },

      /** 新增按钮操作 */
      details(row) {
        this.open = true
        if (row != undefined) {
          this.formTitle = '查看'
          getPlanById({ emergencyPlanId: row.emergencyPlanId }).then(res => {
            if (res.code == 200) {
              this.form = res.data
              this.form.emergencyPlanType = String(this.form.emergencyPlanType)
              //附件显示
            }
          })
        }
      }
    }
  }
</script>
<style lang="scss" scoped>
 

  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .file-item {
    display: flex;
    align-items: center;
    line-height: 25px;
  }

  .file-name {
    color: #1890ff;
    cursor: pointer;
    margin-right: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
</style>
