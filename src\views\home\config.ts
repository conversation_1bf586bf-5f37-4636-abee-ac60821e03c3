export const colors = {
  HP: {
    default: '#3491FA',
    HP018: ['#9FD4FD', '#7BC0FC', '#57A9FB', '#3491FA', '#206CCF', '#114BA3', '#063078'],
    HP034: ['#94BFFF', '#6AA1FF', '#4080FF', '#165DFF', '#0E42D2', '#072CA6', '#031A79'],
    HP010: ['#66D4FF', '#33C5FF', '#00B7FF', '#00A5E5', '#0092CC', '#0080B2', '#006E99'],
    HP007: ['#66EBFF', '#33E4FF', '#00DDFF', '#00C7E5', '#00B1CC', '#009BB2', '#00B1CC'],
    HP017: ['#8FB6F9', '#5F97F7', '#2C76F4', '#0C5BE4', '#0948B4', '#073483', '#042152'],
    HP002: ['#89EDED', '#5AF1F1', '#2DEDED', '#12D4D4', '#0EA5A5', '#0A7676', '#064747'],
    HP003: ['#8FF9DF', '#5FF7D1', '#2CF4C2', '#0CE4AE', '#09B489', '#078364', '#04523F'],
    HP004: ['#8FF9C6', '#5FF7AD', '#2CF494', '#0CE47B', '#09B461', '#078347', '#04522D'],
    HP001: ['#9AF1A6', '#5FF773', '#2CF447', '#0CE429', '#09B420', '#078317', '#04520F'],
    HP005: ['#7BE188', '#4CD263', '#23C343', '#00B42A', '#009A29', '#008026', '#006622'],
    HP006: ['#D1E78F', '#C8EB61', '#B8E532', '#AAD91C', '#91BA18', '#789A14', '#607A10'],

    HP012: ['#9FD4FD', '#7BC0FC', '#57A9FB', '#3491FA', '#206CCF', '#114BA3', '#063078'],
    HP013: ['#94BFFF', '#6AA1FF', '#4080FF', '#165DFF', '#0E42D2', '#072CA6', '#031A79'],
    HP016: ['#66D4FF', '#33C5FF', '#00B7FF', '#00A5E5', '#0092CC', '#0080B2', '#006E99'],
    HP015: ['#66EBFF', '#33E4FF', '#00DDFF', '#00C7E5', '#00B1CC', '#009BB2', '#00B1CC'],
    HP008: ['#8FB6F9', '#5F97F7', '#2C76F4', '#0C5BE4', '#0948B4', '#073483', '#042152'],
    HP009: ['#89EDED', '#5AF1F1', '#2DEDED', '#12D4D4', '#0EA5A5', '#0A7676', '#064747'],
    HP011: ['#8FF9DF', '#5FF7D1', '#2CF4C2', '#0CE4AE', '#09B489', '#078364', '#04523F'],
    HP014: ['#8FF9C6', '#5FF7AD', '#2CF494', '#0CE47B', '#09B461', '#078347', '#04522D'],
    HP019: ['#9AF1A6', '#5FF773', '#2CF447', '#0CE429', '#09B420', '#078317', '#04520F'],
    HP020: ['#7BE188', '#4CD263', '#23C343', '#00B42A', '#009A29', '#008026', '#006622'],
    HP021: ['#D1E78F', '#C8EB61', '#B8E532', '#AAD91C', '#91BA18', '#789A14', '#607A10'],
  },
  RL: {
    default: '#A5D993',
    RL001: ['#C3D9FC', '#94BBFA'],
    RL002: ['#AADAF9', '#7AC5F6'],
    RL003: ['#D8BFF2', '#BE95E9'],
    // ['#BDEEBA', '#95E491'],
    // ['#CFE87A', '#BFE04D'],
    // ['#EEA1C5', '#E675AA'],
    // ['#A0E7E0', '#79DDD3'],
    // ['#F7D194', '#F3BC62'],
    // ['#EEBCEC', '#E495E1'],
    // ['#BCC4EE', '#95A2E4'],
    // ['#DFE993', '#D2E067'],
    // ['#8BE1AE', '#61D790'],
    // ['#A5D993', '#84CB6C'],
    // ['#E2CE9A', '#D6BB71'],
    // ['#E1ADAD', '#D38888'],
  },
  MS: {
    default: '#8D4EDA',
    MS001: ['#DDBEF6', '#C396ED', '#A871E3', '#8D4EDA', '#722ED1', '#551DB0', '#3C108F'],
    MS002: ['#CEA5E9', '#C08DE2', '#B273DC', '#A55CD6', '#9843D0', '#8931C4', '#782BAB'],
    MS003: ['#F08EE6', '#E865DF', '#E13EDB', '#D91AD9', '#B010B6', '#8A0993', '#650370'],
    // ['#FB9DC7', '#F979B7', '#F754A8', '#F5319D', '#CB1E83', '#A11069', '#77064F'],
    // ['#A895EF', '#927AEB', '#7C5EE7', '#6745E3', '#512ADF', '#441FCC', '#3B1BB1'],
    // ['#B995EF', '#A77AEB', '#955EE7', '#8445E3', '#732ADF', '#641FCC', '#571BB1'],
    // ['#D595EF', '#CB7AEB', '#C05EE7', '#B645E3', '#AC2ADF', '#9B1FCC', '#871BB1'],
    // ['#EF95EB', '#EB7AE5', '#E75EE0', '#E345DB', '#DF2AD6', '#CC1FC3', '#B11BAA'],
    // ['#EF95BD', '#EB7AAD', '#E75E9C', '#E3458C', '#DF2A7C', '#CC1F6D', '#B11B5E'],
    // ['#EF95B4', '#EB7AA2', '#E75F8F', '#E3457C', '#DF2A69', '#CC1F5B', '#B11B4F'],
  },
  EX: {
    default: '#F77234',
    EX011: ['#FBACAC', '#F98F8F', '#F87474', '#F65555', '#F53838', '#F41B1B', '#E40B0B'],
    EX010: ['#FCC59F', '#FAAC7B', '#F99057', '#F77234', '#CC5120', '#A23511', '#771F06'],
    EX001: ['#FFCF8B', '#FFB65D', '#FF9A2E', '#FF7D00', '#D25F00', '#A64500', '#792E00'],
    EX002: ['#F9E798', '#FADC6D', '#F9CC45', '#F7BA1E', '#CC9213', '#A26D0A', '#774B04'],
    EX003: ['#FCF482', '#FCF26B', '#FBE842', '#FADC19', '#CFAF0F', '#A38408', '#785D03'],
    EX004: ['#EAC45C', '#E7BA41', '#E3AF25', '#D09F1A', '#B58A17', '#9A7514', '#7F6110'],
    EX005: ['#E6E35C', '#E1DF41', '#DDDA28', '#C7C41F', '#ACAA1B', '#929017', '#777513'],
    EX006: ['#E6CF5C', '#E1C741', '#DDBF28', '#C7AB1F', '#AC941B', '#927D17', '#776613'],
    EX007: ['#E6AE5C', '#E1A141', '#DD9528', '#C7841F', '#AC721B', '#926017', '#774F13'],
    EX008: ['#D6E84A', '#CFE52F', '#C4DA1C', '#ABBE18', '#92A315', '#7A8811', '#626D0E'],

    EX009: ['#FBACAC', '#F98F8F', '#F87474', '#F65555', '#F53838', '#F41B1B', '#E40B0B'],
    EX012: ['#FCC59F', '#FAAC7B', '#F99057', '#F77234', '#CC5120', '#A23511', '#771F06'],
    EX013: ['#FFCF8B', '#FFB65D', '#FF9A2E', '#FF7D00', '#D25F00', '#A64500', '#792E00'],
  },
}

export const getColor = (tabLevel1Code, objectCategoryCode, index) => {
  if (tabLevel1Code === 'RL') {
    return colors[tabLevel1Code][objectCategoryCode][1]
  }

  if (index === -1) return colors[tabLevel1Code].default

  return colors[tabLevel1Code][objectCategoryCode]
    ? colors[tabLevel1Code][objectCategoryCode]?.[index % 7]
    : colors[tabLevel1Code].default
}

export const getIcon = (level1Code, objectCategoryCode, index, code) => {
  if (level1Code === 'RL') {
    return new URL(`/src/assets/images/rightLevel3/RL/${objectCategoryCode}.png`, import.meta.url).href
  }

  // 先按序号找
  let url = new URL(`/src/assets/images/rightLevel3/${level1Code}/${objectCategoryCode}-${(index % 7) + 1}.png`, import.meta.url)
    .href

  // 序号没找到按照code找
  if (url.includes('undefined')) {
    url = new URL(`/src/assets/images/rightLevel3/${level1Code}/${objectCategoryCode}-${code}.png`, import.meta.url).href
  }

  // 都没找到，找二级
  if (url.includes('undefined')) {
    url = new URL(`/src/assets/images/rightLevel2/${objectCategoryCode}.png`, import.meta.url).href
  }
  return url
}
