<template>
  <div class="basic-info">
    <div class="content">
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        layout="horizontal"
        :labelCol="{ span: 12 }"
        :wrapperCol="{ span: 12 }"
        labelAlign="right"
      >
        <a-row :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">
              基本信息
              <a-button style="margin-left: 10px" type="primary" @click="onBtnClick" :loading="loading">
                {{ this.type == 'detail' ? '编辑' : '确定' }}
              </a-button>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">水电站代码：</span>
              <span class="value">{{ data?.projectCode }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">水电站名称：</span>
              <span class="value">{{ data?.projectName }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="行政区划">
              <div class="form-value">{{ data.districtFullName }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="水电站经度(°)">
              <div class="form-value">{{ data.longitude }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="水电站维度(°)">
              <div class="form-value">{{ data.latitude }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :span="24">
            <a-form-model-item label="水电站所在位置" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <div class="form-value" :title="data.location">{{ data.location }}</div>
            </a-form-model-item>
          </a-col>
          <!-- <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="水电站所在位置">
              <div class="form-value" :title="data.location">{{ data.location }}</div>
            </a-form-model-item>
          </a-col> -->

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title second-title">主要特征信息</div>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="水电站类型" prop="hystType">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.hystType"
                placeholder="请选择"
                :options="hystTypeOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">{{ hystTypeOptions.find(el => el.value == data.hystType)?.label }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="总装机容量(kW)">
              <a-input-number v-if="type == 'edit'" v-model="form.totInsCap" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.totInsCap }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="保证出力(kW)">
              <a-input-number v-if="type == 'edit'" v-model="form.firmPow" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.firmPow }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="额定水头(m)">
              <a-input-number v-if="type == 'edit'" v-model="form.ratHead" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.ratHead }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="工程等别">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.engGrad"
                placeholder="请选择"
                :options="engGradOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">{{ engGradOptions.find(el => el.value == data.engGrad)?.label }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="工程规模">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.engScal"
                placeholder="请选择"
                :options="engScalOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">{{ engScalOptions.find(el => el.value == data.engScal)?.label }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="主要建筑物级别">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.mainBuildGrad"
                placeholder="请选择"
                :options="mainBuildGradOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ mainBuildGradOptions.find(el => el.value == data.mainBuildGrad)?.label }}
              </div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="工程建设情况">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.engStat"
                placeholder="请选择"
                :options="engStatOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ engStatOptions.find(el => el.value == data.engStat)?.label }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="开工时间" prop="startDate">
              <a-date-picker
                v-if="type == 'edit'"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                v-model="form.startDate"
                placeholder="请选择"
                style="width: 100%"
              />
              <div v-else class="form-value">{{ data.startDate }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="建成时间" prop="compDate">
              <a-date-picker
                v-if="type == 'edit'"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                placeholder="请选择"
                v-model="form.compDate"
                style="width: 100%"
              />
              <div v-else class="form-value">{{ data.compDate }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="归口管理部门" prop="admDep">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.admDep"
                placeholder="请选择"
                :options="admDepOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ admDepOptions.find(el => el.value == data.admDep)?.label }}
              </div>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="备注" prop="note" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <a-textarea v-if="type == 'edit'" class="area-text" v-model="form.note" placeholder="请输入" allowClear />
              <div v-else class="form-value">{{ data.note }}</div>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
  </div>
</template>

<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getHyst, updateHyst } from './services'
  import { updateListFromStr } from '@/utils/util.js'
  export default {
    name: 'BasicInfo',
    components: {},
    props: {
      projectId: {},
    },
    data() {
      return {
        loading: false,
        data: {},

        pumpTypeOptions: [],
        projectWaitOptions: [],
        projectScaleOptions: [],
        mainBuildGradOptions: [],

        engStatOptions: [], //工程建设情况
        admDepOptions: [], //归口管理部门

        hystTypeOptions: [], //水电站类型
        engGradOptions: [], //工程等别
        engScalOptions: [], //工程规模
        mainBuildGradOptions: [], //主要建筑物级别

        type: 'detail',
        form: {
          admDep: '',
          compDate: '',
          engGrad: '',
          engScal: '',
          engStat: '',
          firmPow: undefined,
          hystType: '',
          id: undefined,
          mainBuildGrad: '',
          note: '',
          projectId: undefined,
          ratHead: undefined,
          startDate: '',
          totInsCap: undefined,
        },
        rules: {},
      }
    },
    created() {
      this.init()
      this.getDataSource()
    },
    methods: {
      init() {
        getOptions('mainBuildGrad').then(res => {
          this.mainBuildGradOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('engScal').then(res => {
          this.engScalOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('projectWait').then(res => {
          this.engGradOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })

        // 归口管理部门-admDep
        getOptions('admDep').then(res => {
          this.admDepOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        // 工程建设情况-engStat
        getOptions('engStat').then(res => {
          this.engStatOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })

        //水电站类型
        getOptions('hystType').then(res => {
          this.hystTypeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
      },
      getDataSource() {
        getHyst({ projectId: this.projectId }).then(res => {
          this.data = res.data
          this.form = {
            ...res.data,
            projectId: this.projectId,
            hystType: res.data.hystType ? `${res.data.hystType}` : undefined,
            admDep: res.data.admDep ? `${res.data.admDep}` : undefined,
            engStat: res.data.engStat ? `${res.data.engStat}` : undefined,
            engScal: res.data.engScal ? `${res.data.engScal}` : undefined,
            engGrad: res.data.engGrad ? `${res.data.engGrad}` : undefined,
            mainBuildGrad: res.data.mainBuildGrad ? `${res.data.mainBuildGrad}` : undefined,
          }
        })
      },

      onBtnClick() {
        if (this.type == 'edit') {
          this.form.projectId = this.projectId
          const params = { ...this.form }

          updateHyst(params)
            .then(res => {
              this.$message.success('修改成功', 3)
              this.getDataSource()
            })
            .finally(() => (this.loading = false))
        }

        this.type = this.type == 'detail' ? 'edit' : 'detail'
      },
    },
  }
</script>

<style lang="scss" scoped>
 

  @import url('~@/assets/styles/basic-info.scss');
</style>
