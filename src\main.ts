import { createApp } from 'vue'
import AppVue from './App.vue'

// unocss
import 'uno.css'

// svg icon
import 'virtual:svg-icons-register'

// 全局样式
import '@/styles/main.scss'

// Ant Design Vue
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'

// iconify
import { enableCache, disableCache } from '@iconify/vue'

import { setupRouter } from './router'
import { setupStore } from './store/index.ts'
import { setupDirectives } from './directives'
import { setupLoading, setupDayjs, setupNaiveDiscreteApi } from './core'

async function setupApp() {
  await setupLoading()

  // dayjs
  setupDayjs()

  // Naive UI
  setupNaiveDiscreteApi()

  // iconify/vue <Icon>组件禁止缓存
  // enableCache('session') // 默认local
  // disableCache('all')

  const app = createApp(AppVue)

  // 使用 Ant Design Vue
  app.use(Antd)

  await setupStore(app)

  await setupRouter(app)

  // 自定义指令
  setupDirectives(app)

  app.mount('#app')
}

setupApp()
