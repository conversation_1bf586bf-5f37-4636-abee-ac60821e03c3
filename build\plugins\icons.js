import { globSync } from 'glob'
import path from 'path'

/**
 * @usage 生成icons, 用于 unocss safelist，以支持页面动态渲染自定义图标
 */
export function getIcons() {
  const meFiles = globSync('src/assets/svgs/*.svg', { nodir: true, strict: true })

  const meIcons = meFiles.map(filePath => {
    const fileName = path.basename(filePath) // 获取文件名，包括后缀
    const fileNameWithoutExt = path.parse(fileName).name // 获取去除后缀的文件名
    return `i-me:${fileNameWithoutExt}`
  })

  return [...meIcons]
}

const PLUGIN_ICONS_ID = 'icons'
export function pluginIcons() {
  return {
    name: PLUGIN_ICONS_ID,
    resolveId(id) {
      if (id === PLUGIN_ICONS_ID) return '\0' + PLUGIN_ICONS_ID
    },
    load(id) {
      if (id === '\0' + PLUGIN_ICONS_ID) {
        return `export default ${JSON.stringify(getIcons())}`
      }
    },
  }
}
