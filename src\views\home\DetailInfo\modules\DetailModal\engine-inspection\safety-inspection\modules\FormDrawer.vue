<template>
  <ant-modal :visible="open" :modal-title="formTitle" modalWidth="800" @cancel="cancel" modalHeight="700">
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">基本信息</div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="名称" prop="inspectionName">
              <a-input v-model="form.inspectionName" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="计划类型" prop="planType">
              <a-select show-search placeholder="请输入" v-model="form.planType" option-filter-prop="children">
                <a-select-option v-for="item in planTypeOptions" :key="item.key" :value="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="检查类型" prop="inspectionType">
              <a-select show-search placeholder="请输入" v-model="form.inspectionType" option-filter-prop="children">
                <a-select-option v-for="item in inspectionTypeOptions" :key="item.key" :value="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="计划完成时间" prop="planCompleteTime">
              <a-date-picker
                v-model="form.planCompleteTime"
                format="YYYY-MM-DD"
                placeholder="请选择"
                style="width: 100%"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="所属工程" prop="projectId">
              <a-tree-select
                v-model="form.projectId"
                style="width: 100%"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                :tree-data="projectOptions"
                show-search
                treeNodeFilterProp="title"
                allowClear
                placeholder="请选择"
                :replaceFields="{
                  children: 'children',
                  title: 'projectName',
                  key: 'projectId',
                  value: 'projectId'
                }"
                tree-default-expand-all
              ></a-tree-select>
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="发起原因" prop="reason">
              <a-textarea v-model="form.reason" placeholder="请输入" allow-clear :row="3" />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">申请文件附件</div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="">
              <UploadFile
                :fileUrl.sync="form.applyForAttaches"
                :multiple="true"
                listType="text"
                folderName="projectCover"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">检查记录单附件</div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="">
              <UploadFile
                :fileUrl.sync="form.checkAttaches"
                :multiple="true"
                listType="text"
                folderName="projectCover"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">总结报告附件</div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="">
              <UploadFile
                :fileUrl.sync="form.summaryAttaches"
                :multiple="true"
                listType="text"
                folderName="projectCover"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">结果</div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="检查结果" prop="inspectionStatus">
              <a-select show-search placeholder="请输入" v-model="form.inspectionStatus" option-filter-prop="children">
                <a-select-option v-for="item in inspectionStatusOptions" :key="item.key" :value="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
      <!-- <a-button type="primary" @click="submitForm">保存</a-button> -->
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addInspection, editInspection, getInspectionById } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'

  export default {
    name: 'FormDrawer',
    components: { AntModal, UploadFile },
    props: ['projectOptions', 'inspectionTypeOptions', 'planTypeOptions', 'inspectionStatusOptions'],
    data() {
      return {
        loading: false,
        formTitle: '',
        form: {
          applyForAttaches: [],
          checkAttaches: [],
          inspectionId: null,
          inspectionName: '',
          inspectionStatus: null,
          inspectionType: null,
          planCompleteTime: '',
          planType: null,
          projectId: null,
          reason: '',
          summaryAttaches: []
        },
        open: false,
        rules: {
          inspectionName: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
          planType: [{ required: true, message: '计划类型不能为空', trigger: 'change' }],
          inspectionStatus: [{ required: true, message: '检查结果不能为空', trigger: 'change' }],
          inspectionType: [{ required: true, message: '检查类型不能为空', trigger: 'change' }],
          reason: [{ required: true, message: '发起原因不能为空', trigger: 'blur' }],
          planCompleteTime: [{ required: true, message: '计划完成时间不能为空', trigger: 'change' }],
          projectId: [{ required: true, message: '所属工程不能为空', trigger: 'change' }]
        }
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
          this.planDate = []
          this.taskDate = []
        }
      },
      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = '新增'
        if (row != undefined) {
          this.formTitle = '修改'
          getInspectionById({ inspectionId: row.inspectionId }).then(res => {
            if (res.code == 200) {
              //附件显示
              this.form = {
                ...res.data,
                applyForAttaches: res.data.applyForAttaches?.map(el => el.attachUrl),
                checkAttaches: res.data.checkAttaches?.map(el => el.attachUrl),
                summaryAttaches: res.data.summaryAttaches?.map(el => el.attachUrl)
              }
              this.form.inspectionType = String(this.form.inspectionType)
            }
          })
        }
      },

      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            // this.form.startTime = moment(this.form.startTime).format('YYYY-MM-DD')
            this.form.planCompleteTime = moment(this.form.planCompleteTime).format('YYYY-MM-DD')
            if (this.form.inspectionId == null) {
              addInspection(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              editInspection(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            }
          }
        })
      }
    }
  }
</script>
<style lang="scss" scoped>
  .title {
    font-size: 16px;
    font-weight: 700;
  }
</style>
