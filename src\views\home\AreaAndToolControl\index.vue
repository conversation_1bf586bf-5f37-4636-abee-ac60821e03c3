<template>
  <div class="control" :style="{ left: isShow.isShowLeft ? (attrs.isExpand ? '6.5rem' : '4.4rem') : '0.2rem' }">
    <div class="flex-1 b-r-(1px dashed #E5E6EB)" id="control-tree">
      <n-tree-select
        :options="treeData"
        filterable
        default-expand-all
        :consistent-menu-width="false"
        key-field="districtCode"
        label-field="districtName"
        v-model:value="treeValue"
        @update:value="handleUpdateValue"
        @focus="handleFocusInput"
      />
    </div>
    <div class="w-0.76rem" id="control-tool">
      <n-dropdown trigger="hover" :options="options">
        <div cursor-pointer>
          <MyIcon name="tool-box" class="text-0.18rem mx-0.1rem" />
          <span class="c-text_md text-0.12rem">工具箱</span>
        </div>
      </n-dropdown>
    </div>
  </div>
</template>

<script setup lang="tsx" name="AreaAndToolControl">
  import { getValueByKey } from '@/api'
  import { districtGetTree } from '../services'
  import axios from 'axios'

  const isShow = defineModel()
  let treeData = $ref([])
  let treeValue = $ref(null)
  const currentDistrict = defineModel('currentDistrict')
  const isDataMode = defineModel('isDataMode')
  const attrs = useAttrs()

  getValueByKey('map.district.default').then(resp => {
    districtGetTree().then(res => {
      treeData = (res.data || [])?.[0].children

      treeValue = resp.data || treeData[0].districtCode
      handleUpdateValue(treeValue)
    })
  })

  const options = computed(() => [
    {
      label: () => (
        <n-checkbox onClick={e => e.stopPropagation()} v-model:checked={isShow.value.isShowLeft} style='user-select: none'>
          导航
        </n-checkbox>
      ),
      key: 'nav',
    },
    {
      label: () => (
        <n-checkbox onClick={e => e.stopPropagation()} v-model:checked={isDataMode.value} style='user-select: none'>
          数据
        </n-checkbox>
      ),
      key: 'data',
    },
    {
      label: () => (
        <n-checkbox onClick={e => e.stopPropagation()} v-model:checked={isShow.value.isShowRight} style='user-select: none'>
          图层
        </n-checkbox>
      ),
      key: 'layer',
    },
    {
      label: () => (
        <n-checkbox onClick={e => e.stopPropagation()} v-model:checked={isShow.value.isShowTool} style='user-select: none'>
          工具
        </n-checkbox>
      ),
      key: 'tool',
    },
    {
      label: () => (
        <n-checkbox onClick={e => e.stopPropagation()} v-model:checked={isShow.value.isShowLabel} style='user-select: none'>
          标注
        </n-checkbox>
      ),
      key: 'label',
    },
  ])

  const handleUpdateValue = value => {
    currentDistrict.value = value
  }

  // 开发时用来获取dom，以便查看class
  const handleFocusInput = () => {
    // nextTick(() => {
    //   setTimeout(() => {
    //     console.log('-----', document.querySelector('.n-tree-select-menu'))
    //     console.log(',,,,,,', document.querySelector('.n-tree.n-tree--block-node'))
    //   }, 1000)
    // })
  }
</script>
<style lang="scss" scoped>
  .control {
    position: absolute;
    left: 4.4rem;
    top: 0.2rem;
    z-index: 999;
    height: 0.45rem;
    width: 2.8rem;
    background-color: #ffffff;
    border-radius: 0.04rem;
    box-shadow: 0px 0.04rem 0.1rem 0px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    padding: 0.12rem;

    transition: all 0.3s;

    :deep(.n-base-selection) {
      min-height: unset;
      font-size: 0.14rem;
      .n-base-selection-label {
        height: auto;
      }
      .n-base-selection__border {
        border-color: transparent;
      }
      .n-base-selection__state-border {
        border-color: transparent;
        box-shadow: unset;
      }
    }
  }
</style>

<style lang="scss">
  .n-tree-select-menu {
    margin-top: 0.2rem !important;
    .n-tree {
      font-size: 0.14rem;
      .n-tree-node-wrapper {
        padding: 0.03rem;

        .n-tree-node-indent > div {
          width: 0.16rem !important;
        }
        .n-tree-node-switcher {
          width: 0.24rem !important;
          height: 0.24rem !important;
          .n-tree-node-switcher__icon {
            width: 0.14rem;
            height: 0.14rem;
            font-size: 0.14rem;
          }
        }
        .n-tree-node-content {
          min-height: 0.24rem;
          padding: 0 0.06rem 0 0.04rem;
          line-height: unset;
        }
      }
    }
  }

  .n-dropdown-menu {
    margin-top: 0.2rem !important;
    padding: 0.04rem 0;
    .n-dropdown-option {
      .n-dropdown-option-body {
        height: 0.34rem;
        line-height: 0.34rem;
        font-size: 0.14rem;
        &::before {
          left: 0.04rem;
          right: 0.04rem;
        }
        .n-dropdown-option-body__prefix {
          width: 0, 0.14rem;
        }
        .n-dropdown-option-body__suffix {
          min-width: 0.14rem;
          padding: 0 0.08rem;
        }

        .n-checkbox {
          font-size: 0.14rem;
          .n-checkbox-box-wrapper {
            width: 0.16rem;
            .n-checkbox-box {
              width: 0.16rem;
              height: 0.16rem;
            }
          }
        }
      }
    }
  }
</style>
