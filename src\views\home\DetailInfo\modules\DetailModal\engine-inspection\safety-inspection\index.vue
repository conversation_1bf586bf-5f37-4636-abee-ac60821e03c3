<template>
  <div style="height: 100%">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="名称">
        <a-input
          v-model="queryParam.inspectionName"
          placeholder="请输入"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item>

      <a-form-item label="检查类型">
        <a-select show-search placeholder="请输入" v-model="queryParam.inspectionType" option-filter-prop="children">
          <a-select-option v-for="item in inspectionTypeOptions" :key="item.key" :value="item.key">
            {{ item.value }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="计划完成时间">
        <a-range-picker
          allow-clear
          :value="planTimes"
          format="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          @change="onRangeChange"
        />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button"></div>
        </VxeTable>
        <FormDrawer
          v-if="showForm"
          ref="formRef"
          :inspectionTypeOptions="inspectionTypeOptions"
          :projectOptions="projectOptions"
          :planTypeOptions="planTypeOptions"
          :inspectionStatusOptions="inspectionStatusOptions"
          @ok="onOperationComplete"
          @close="showForm = false"
        />
        <FormDetails
          v-if="showFormDetails"
          ref="formDetailsRef"
          :inspectionTypeOptions="inspectionTypeOptions"
          :projectOptions="projectOptions"
          :planTypeOptions="planTypeOptions"
          :inspectionStatusOptions="inspectionStatusOptions"
          @ok="onOperationComplete"
          @close="showFormDetails = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getOptions, getProjectTree } from '@/api/common'
  import { getInspectionPage, deleteInspection } from './services'
  import FormDrawer from './modules/FormDrawer.vue'
  import FormDetails from './modules/FormDetails.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import getFlatTreeMap from '@/utils/getMapFlatTree'

  export default {
    name: 'SafetyInspection',
    components: {
      VxeTable,
      VxeTableForm,
      FormDetails,
      FormDrawer
    },
    data() {
      return {
        inspectionStatusOptions: [
          { key: 1, value: '未判定' },
          { key: 2, value: '正常' },
          { key: 3, value: '异常' }
        ],
        planTypeOptions: [
          { key: 1, value: '计划内' },
          { key: 2, value: '计划外' }
        ],
        isChecked: false,
        inspectionTypeOptions: [],
        inspectionTypes: [],
        inspectionStatuses: [],
        planTypes: [],
        projects: [],
        projectOptions: [],
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        showForm: false,
        showFormDetails: false,
        planTimes: [],
        archivesOptions: [],

        list: [],
        tableTitle: '安全检查',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          endTime: '',
          inspectionName: null,
          inspectionType: null,
          pageNum: 1,
          pageSize: 10,
          projectId: null,
          sort: [],
          startTime: ''
        },
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '安全检查名称',
            field: 'inspectionName',
          },
          {
            title: '计划类型',
            field: 'planType',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.planTypes[row.planType]?.value || ''
              }
            }
          },
          {
            title: '检查类型',
            field: 'inspectionType',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.inspectionTypes[row.inspectionType]?.value || ''
              }
            }
          },

          {
            title: '计划完成时间',
            field: 'planCompleteTime',
          },
          {
            title: '检查结果',
            field: 'inspectionStatus',
            slots: {
              default: ({ row, rowIndex }) => {
                let statusPointColor = 'common-status-incomplete'
                if (row.inspectionStatus == 2) {
                  statusPointColor = 'common-status-completed'
                } else if (row.inspectionStatus == 3) {
                  statusPointColor = 'common-status-abnormal'
                }
                return (
                  <div class='common-status-box'>
                    <i class={['common-status-icon', statusPointColor]}></i>
                    <span>{this.inspectionStatuses[row.inspectionStatus]?.value}</span>
                  </div>
                )
              },
            },
          },
          {
            title: '所属工程',
            field: 'projectName',
          },

          {
            title: '操作',
            field: 'operate',
            width: 138,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetails(row)}>查看</a>
                  </span>
                )
              }
            }
          }
        ]
      }
    },
    created() {
      this.getList()
      getOptions('inspectionType').then(res => {
        this.inspectionTypeOptions = res.data
        this.inspectionTypes = getFlatTreeMap(this.inspectionTypeOptions, 'key')
      })

      // 获取工程树
      getProjectTree({ objectCategoryId: undefined }).then(res => {
        this.projectOptions = res.data
        this.projects = getFlatTreeMap(this.projectOptions, 'id')
      })
      this.planTypes = getFlatTreeMap(this.planTypeOptions, 'key')
      this.inspectionStatuses = getFlatTreeMap(this.inspectionStatusOptions, 'key')
    },
    mounted() {},
    methods: {
      // 弹出日历和关闭日历的回调
      openChangeOne(status) {
        if (status) {
          this.yearShowOne = true
        }
      },
      // 得到年份选择器的值
      panelChangeOne(value) {
        this.queryParam.year = moment(value).format('YYYY')
        this.yearShowOne = false
      },
      onRangeChange(value, planTimes) {
        this.planTimes = value
        if (planTimes.length == 0) {
          return
        }
        this.queryParam.startTime = planTimes[0] ? moment(planTimes[0]).format('YYYY-MM-DD') : undefined
        this.queryParam.endTime = planTimes[1] ? moment(planTimes[1]).format('YYYY-MM-DD') : undefined
      },
      /** 查询列表 */
      getList() {
        this.showForm = false
        this.showFormDetails = false
        this.loading = true
        this.selectChange({ records: [] })
        getInspectionPage({ ...this.queryParam, projectId: this.$attrs.projectId }).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      //
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records?.map(item => item.inspectionId)
        this.names = valObj.records?.map(item => item.inspectionName)
        this.isChecked = !!valObj.records.length
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          endTime: '',
          inspectionName: null,
          inspectionType: null,
          pageNum: 1,
          pageSize: 10,
          projectId: null,
          sort: [],
          startTime: ''
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 导出
      handleExport() {},
      /* 新增 */
      handleAdd() {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle())
      },
      /* 修改 */
      handleEdit(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      /* 查看 */
      handleDetails(record) {
        this.showFormDetails = true
        this.$nextTick(() => this.$refs.formDetailsRef.details(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.inspectionId ? [row?.inspectionId] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteInspection({ inspectionIds: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {}
        })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      }
    }
  }
</script>
<style lang="scss" scoped></style>
