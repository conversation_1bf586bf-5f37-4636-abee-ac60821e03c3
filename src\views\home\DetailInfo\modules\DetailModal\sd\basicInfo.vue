<template>
  <div class="basic-info">
    <div class="content">
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        layout="horizontal"
        :labelCol="{ span: 12 }"
        :wrapperCol="{ span: 12 }"
        labelAlign="right"
      >
        <a-row :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">
              基本信息
              <a-button style="margin-left: 10px" type="primary" @click="onBtnClick" :loading="loading">
                {{ this.type == 'detail' ? '编辑' : '确定' }}
              </a-button>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">淤地坝代码：</span>
              <span class="value">{{ data?.projectCode }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">淤地坝名称：</span>
              <span class="value">{{ data?.projectName }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="行政区划">
              <div class="form-value">{{ data.districtFullName }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :span="24">
            <a-form-model-item label="淤地坝所在位置" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <div class="form-value" :title="data.location">{{ data.location }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="起点经度(°)">
              <a-input-number v-if="type == 'edit'" v-model="form.startLong" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.startLong }}</div>
              <!-- <div class="form-value">{{ data.longitude }}</div> -->
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="起点维度(°)">
              <a-input-number v-if="type == 'edit'" v-model="form.startLat" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.startLat }}</div>
              <!-- <div class="form-value">{{ data.latitude }}</div> -->
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="终点经度(°)">
              <a-input-number v-if="type == 'edit'" v-model="form.endLong" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.endLong }}</div>
              <!-- <div class="form-value">{{ data.longitude }}</div> -->
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="终点维度(°)">
              <a-input-number v-if="type == 'edit'" v-model="form.endLat" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.endLat }}</div>
              <!-- <div class="form-value">{{ data.latitude }}</div> -->
            </a-form-model-item>
          </a-col>

          <!-- <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="淤地坝所在位置">
              <div class="form-value" :title="data.location">{{ data.location }}</div>
            </a-form-model-item>
          </a-col> -->

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title second-title">主要特征信息</div>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="总库容规模">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.totCapLevel"
                placeholder="请选择"
                :options="totCapLevelOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ totCapLevelOptions.find(el => el.value == data.totCapLevel)?.label }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="控制汇流面积(k㎡)">
              <a-input-number v-if="type == 'edit'" v-model="form.conArea" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.conArea }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="总库容(10⁴m³)">
              <a-input-number v-if="type == 'edit'" v-model="form.totCap" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.totCap }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="坝顶长度(m)">
              <a-input-number v-if="type == 'edit'" v-model="form.damTopLen" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.damTopLen }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="坝高(m)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.damSizeHig"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.damSizeHig }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :span="24">
            <a-form-model-item label="备注" prop="note" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <a-textarea v-if="type == 'edit'" class="area-text" v-model="form.note" placeholder="请输入" allowClear />
              <div v-else class="form-value">{{ data.note }}</div>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
  </div>
</template>

<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getSd, updateSd } from './services'
  import { updateListFromStr } from '@/utils/util.js'
  export default {
    name: 'BasicInfo',
    components: {},
    props: {
      projectId: {},
    },
    data() {
      return {
        loading: false,
        data: {},

        totCapLevelOptions: [], //总库容规模

        type: 'detail',
        form: {
          conArea: undefined,
          damSizeHig: undefined,
          damTopLen: undefined,
          endLat: '',
          endLong: '',
          id: undefined,
          note: '',
          projectId: undefined,
          startLat: '',
          startLong: '',
          totCap: undefined,
          totCapLevel: '',
        },
        rules: {},
      }
    },
    created() {
      this.init()
      this.getDataSource()
    },
    methods: {
      init() {
        getOptions('totCapLevel').then(res => {
          this.totCapLevelOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
      },
      getDataSource() {
        getSd({ projectId: this.projectId }).then(res => {
          this.data = res.data
          this.form = {
            ...res.data,
            projectId: this.projectId,
            totCapLevel: res.data.totCapLevel ? res.data.totCapLevel : undefined,
          }
        })
      },

      onBtnClick() {
        if (this.type == 'edit') {
          this.form.projectId = this.projectId
          const params = { ...this.form }

          updateSd(params)
            .then(res => {
              this.$message.success('修改成功', 3)
              this.getDataSource()
            })
            .finally(() => (this.loading = false))
        }

        this.type = this.type == 'detail' ? 'edit' : 'detail'
      },
    },
  }
</script>

<style lang="less" scoped>
  @import url('~@/global.less');

  @import url('~@/assets/styles/basic-info.less');
</style>
