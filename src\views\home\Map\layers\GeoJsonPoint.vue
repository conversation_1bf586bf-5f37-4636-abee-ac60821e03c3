<template></template>
<script setup lang="tsx" name="GeoJsonPoint">
  import { GeoJsonLayer } from '@deck.gl/layers'
  import { hexToRgb } from '../utils/toDeckglRgb.js'
  import overlayManager from '../utils/overlayManager.js'
  import marker from '@/assets/svgs/marker.svg'

  const attrs: any = useAttrs()
  const activeItem = defineModel('activeItem')
  const lastZoom = ref(attrs.zoom)
  const lastActive = ref(null)

  // 使用全局 overlay 管理器，不再创建独立的 overlay
  const layerId = `point-layer-${attrs.id}`

  const updateProps = () => {
    // 创建图层
    const layer = new GeoJsonLayer({
          id: 'geojson-layer-point-' + attrs.id,
          data: JSON.parse(JSON.stringify(attrs.geojson)),
          filled: true,
          pickable: true,
          pointType: 'circle+icon+text',

          // 处理 circle 描边
          stroked: attrs.zoom <= 12, // 是否描边
          getLineColor: [255, 255, 255, 255], // 描边颜色
          lineWidthMaxPixels: 1, // 周围最大线宽
          lineWidthMinPixels: 1, // 周围最小线宽
          // 处理 circle 实心
          getFillColor: d => {
            if (activeItem.value?.id === d.properties.object_id) {
              return [...hexToRgb('#F5DD7E'), 255]
            }

            return [...hexToRgb(d.properties.color), 255]
          },
          getPointRadius: d => {
            if (attrs.zoom <= 12) return 8 // 点半径
            return 0
          },
          pointRadiusMinPixels: attrs.zoom <= 12 ? 5 : 0, // 最小半径
          pointRadiusMaxPixels: 25, // 最大半径
          // 处理 icon
          getIcon: d => {
            if (attrs.zoom > 12) {
              return { url: d.properties.icon, width: 36, height: 36, anchorY: 36 }
            }

            return { url: ' ', width: 36, height: 36 }
          },
          getIconSize: [20, 22],
          iconSizeMinPixels: 20,

          // 处理 text
          getText: d => {
            if (attrs.zoom <= 12) return ''
            return d.properties?.object_name
          },
          getTextAnchor: 'start',
          getTextPixelOffset: [11, -10],
          getTextColor: [29, 33, 41, 255],
          textCharacterSet: 'auto',
          getTextSize: 12,
          textOutlineColor: [255, 255, 255, 255],
          textOutlineWidth: 7,
          textFontSettings: { sdf: true, smoothing: 0.3 },

          onClick: opt => {
            const options = JSON.parse(JSON.stringify(opt.object))
            if (activeItem.value?.id === options.properties.object_id) {
              activeItem.value = { gisLayer: activeItem.value.gisLayer }
            } else {
              activeItem.value = {
                tabVal1: attrs.tabLevel1,
                tabVal2: attrs.tabLevel2,
                ...options.properties,
                name: options.properties.object_name,
                id: options.properties.object_id,
                gisLayer: attrs.gisLayer,
                isLeft: attrs.isLeft,
              }
            }
          },
          // onHover: opt => {
          //   const canvas = mapIns.getCanvas()
          //   if (opt?.object) {
          //     canvas.style.cursor = 'pointer'
          //   } else {
          //     canvas.style.cursor = ''
          //   }
          // },
        })

    // 使用全局 overlay 管理器添加图层
    overlayManager.addLayer(layerId, layer)
  }

  watch(
    () => attrs.geojson,
    newVal => {
      nextTick(() => {
        updateProps()
      })
    },
  )

  watch(
    () => activeItem.value,
    newVal => {
      // 切换其他图层时，上一次选中的是当前图层
      if (attrs.gisLayer === lastActive.value?.gisLayer) {
        updateProps()
      }
      // 选中和取消当前图层
      if (attrs.gisLayer === newVal?.gisLayer) {
        updateProps()
      }

      nextTick(() => {
        lastActive.value = { ...newVal }
      })
    },
  )

  // 缩放时
  watch(
    () => attrs.zoom,
    newVal => {
      // if (newVal < 8) {
      //   stateRef.value.deckOverlay.setProps({ layers: [] })
      // } else {
      //   // if (stateRef.value.deckOverlay?._props?.layers?.length > 0) return
      // }

      if (lastZoom.value > 12 && newVal <= 12) {
        nextTick(() => updateProps())
      }
      if (lastZoom.value <= 12 && newVal > 12) {
        nextTick(() => updateProps())
      }

      lastZoom.value = newVal
    },
  )

  onMounted(() => {
    updateProps()
    // 通知父组件图层已挂载，传递 overlay 管理器的引用
    attrs.onMounted && attrs.onMounted(attrs.id, overlayManager)
  })

  onBeforeUnmount(() => {
    // 组件销毁时移除图层
    overlayManager.removeLayer(layerId)
  })
</script>
