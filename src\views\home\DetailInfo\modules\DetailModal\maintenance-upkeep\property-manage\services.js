import request from '@/utils/request'

// 维修养护_物业管理-列表分页查询
export function getMaintenancePage(data) {
  return request({
    url: '/prjstd/maintenance/page',
    method: 'post',
    data
  })
}
// 增加
export function addMaintenance(data) {
  return request({
    url: '/prjstd/maintenance/add',
    method: 'post',
    data
  })
}
// 详情
export function getMaintenanceById(params) {
  return request({
    url: '/prjstd/maintenance/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
// 更新
export function editMaintenance(data) {
  return request({
    url: '/prjstd/maintenance/update',
    method: 'post',
    data
  })
}
// 删除
export function deleteMaintenance(params) {
  return request({
    url: '/prjstd/maintenance/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

//物业管理列表
export function getPropertyList(data) {
  return request({
    url: '/prjstd/property/page',
    method: 'post',
    data
  })
}
