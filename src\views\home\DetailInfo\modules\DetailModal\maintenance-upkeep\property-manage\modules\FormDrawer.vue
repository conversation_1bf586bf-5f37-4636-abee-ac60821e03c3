<template>
  <ant-modal :visible="open" :modal-title="formTitle" modalWidth="800" @cancel="cancel" modalHeight="700">
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">基本信息</div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="物业或外委单位" prop="propertyId">
              <a-select show-search placeholder="请输入" v-model="form.propertyId" option-filter-prop="children">
                <a-select-option v-for="item in propertyList" :key="item.propertyId" :value="item.propertyId">
                  {{ item.propertyName }}
                </a-select-option>
              </a-select>
              <!-- <a-input v-model="form.propertyId" placeholder="请输入" allow-clear /> ,'propertyList' -->
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="合同名称" prop="contractName">
              <a-input v-model="form.contractName" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <!-- <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="计划类型" prop="planType">
              <a-select show-search placeholder="请输入" v-model="form.planType" option-filter-prop="children">
                <a-select-option v-for="item in planTypeOptions" :key="item.key" :value="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col> -->

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="开始时间" prop="startTime">
              <a-date-picker v-model="form.startTime" format="YYYY-MM-DD" placeholder="请选择" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="结束时间" prop="endTime">
              <a-date-picker v-model="form.endTime" format="YYYY-MM-DD" placeholder="请选择" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="资金(万元)" prop="propertyCapital">
              <a-input-number
                :precision="2"
                :min="0"
                style="width: 100%"
                v-model="form.propertyCapital"
                placeholder="请输入"
                allow-clear
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="所属工程" prop="projectId">
              <a-tree-select
                v-model="form.projectId"
                style="width: 100%"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                :tree-data="projectOptions"
                show-search
                treeNodeFilterProp="title"
                allowClear
                placeholder="请选择"
                :replaceFields="{
                  children: 'children',
                  title: 'projectName',
                  key: 'projectId',
                  value: 'projectId',
                }"
                tree-default-expand-all
              ></a-tree-select>
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">合同文件附件</div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="">
              <UploadFile
                :fileUrl.sync="form.contractAttaches"
                :multiple="true"
                listType="text"
                folderName="projectCover"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">阶段计划附件</div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="">
              <UploadFile
                @getFileUrl="urls => (form.stageAttaches = urls)"
                :fileUrl="form.stageAttaches"
                :multiple="true"
                listType="text"
                folderName="projectCover"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">月度考核报告附件</div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="">
              <UploadFile
                @getFileUrl="urls => (form.monthlyAttaches = urls)"
                :fileUrl="form.monthlyAttaches"
                :multiple="true"
                listType="text"
                folderName="projectCover"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">季度考核报告附件</div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="">
              <UploadFile
                @getFileUrl="urls => (form.quarterlyAttaches = urls)"
                :fileUrl="form.quarterlyAttaches"
                :multiple="true"
                listType="text"
                folderName="projectCover"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">验收材料附件</div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="">
              <UploadFile
                @getFileUrl="urls => (form.acceptanceAttaches = urls)"
                :fileUrl="form.acceptanceAttaches"
                :multiple="true"
                listType="text"
                folderName="projectCover"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <!-- <a-button type="primary" @click="submitForm">保存</a-button> -->
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addMaintenance, editMaintenance, getMaintenanceById } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'

  export default {
    name: 'FormDrawer',
    components: { AntModal, UploadFile },
    props: ['projectOptions', 'inspectionTypeOptions', 'propertyList'],
    data() {
      return {
        loading: false,
        formTitle: '',
        form: {
          acceptanceAttaches: [],
          contractAttaches: [],
          contractName: '',
          endTime: '',
          maintenancePropertyId: null,
          monthlyAttaches: [],
          projectId: null,
          propertyCapital: null,
          propertyId: null,
          quarterlyAttaches: [],
          stageAttaches: [],
          startTime: '',
        },
        open: false,
        rules: {
          contractName: [{ required: true, message: '合同名称不能为空', trigger: 'blur' }],
          propertyId: [{ required: true, message: '物业或外委单位不能为空', trigger: 'change' }],

          propertyCapital: [{ required: true, message: '资金(万元)不能为空', trigger: 'blur' }],

          startTime: [{ required: true, message: '开始时间不能为空', trigger: 'change' }],
          endTime: [{ required: true, message: '结束时间不能为空', trigger: 'change' }],
          projectId: [{ required: true, message: '所属工程不能为空', trigger: 'change' }],
        },
      }
    },
    created() {
      // console.log('propertyList propertyId', this.propertyList)
    },
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
          this.planDate = []
          this.taskDate = []
        }
      },
      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = '新增'
        if (row != undefined) {
          this.formTitle = '修改'
          getMaintenanceById({ maintenancePropertyId: row.maintenancePropertyId }).then(res => {
            if (res.code == 200) {
              //附件显示
              this.form = {
                ...res.data,
                contractAttaches: res.data.contractAttaches?.map(el => el.attachUrl),
                acceptanceAttaches: res.data.acceptanceAttaches?.map(el => el.attachUrl),
                monthlyAttaches: res.data.monthlyAttaches?.map(el => el.attachUrl),
                quarterlyAttaches: res.data.quarterlyAttaches?.map(el => el.attachUrl),
                stageAttaches: res.data.stageAttaches?.map(el => el.attachUrl),
              }
              this.form.propertyId = this.propertyList.find(el => el.propertyId == this.form.propertyId)?.propertyId
            }
          })
        }
      },

      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            this.form.startTime = moment(this.form.startTime).format('YYYY-MM-DD')
            this.form.endTime = moment(this.form.endTime).format('YYYY-MM-DD')
            if (this.form.maintenancePropertyId == null) {
              addMaintenance(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              editMaintenance(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            }
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .title {
    font-size: 16px;
    font-weight: 700;
  }
</style>
