<template>
  <VxeTable
    ref="vxeTableRef"
    tableTitle="维养记录"
    :columns="columns"
    :tableData="list"
    :loading="loading"
    :isAdaptPageSize="false"
    @refresh="getList"
    @selectChange="selectChange"
    @sortChange="sortChange"
    :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
    @handlePageChange="handlePageChange"
  ></VxeTable>
</template>

<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getMaintenanceById } from '../../services'
  import VxeTable from '@/components/VxeTable'

  export default {
    name: 'MaintenanceRecords',
    props: ['projectId'],
    components: {
      VxeTable,
    },
    data() {
      return {
        loading: false,
        total: 0,
        list: [],
        statusOptions: [],

        queryParam: {
          deptId: undefined,
          endTime: undefined,
          objectCategoryCode: 'HP',
          objectId: this.projectId,
          pageNum: 1,
          pageSize: 10,
          serialNumber: undefined,
          sort: [],
          startTime: undefined,
          status: undefined,
        },
        columns: [
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '维养单编号',
            field: 'serialNumber',
            minWidth: 100,
          },
          {
            title: '维养单位',
            field: 'deptName',
            minWidth: 100,
          },
          {
            title: '维养范围',
            field: 'objectNames',
            minWidth: 100,
          },
          {
            title: '维养时间',
            field: 'maintenanceDate',
            minWidth: 100,
          },
          {
            title: '维养状态',
            field: 'status',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return this.statusOptions.find(el => el.value == row.status)?.label
              },
            },
          },
          {
            title: '维养人员',
            field: 'userNames',
            minWidth: 100,
          },
          {
            title: '维养内容',
            field: 'content',
            minWidth: 100,
          },
        ],
      }
    },
    created() {
      getOptions('maintenanceStatus').then(res => {
        this.statusOptions = (res?.data || []).map(el => ({ label: el.value, value: el.key }))
        this.getList()
      })
    },
    methods: {
      /** 查询列表 */
      getList() {
        this.showFormMaintenance = false
        this.loading = true
        getMaintenanceById(this.queryParam).then(response => {
          this.list = response?.data?.data

          this.total = response.data.total
          this.loading = false
        })
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        // this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.id)
        this.names = valObj.records.map(item => item.serialNumber)
        this.isChecked = !!valObj.records.length
      },
    },
  }
</script>
<style lang="scss" scoped>
  .table-operations {
    .ant-btn {
      &:last-child {
        margin-right: 0px;
      }
    }
  }
</style>
