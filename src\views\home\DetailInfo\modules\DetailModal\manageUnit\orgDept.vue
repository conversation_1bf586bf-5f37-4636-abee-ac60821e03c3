<template>
  <div class="org-dept">
    <div style="padding: 20px">
      <div class="title">岗位信息</div>
      <MyRelation
        :dataSource="orgData"
        :projectName="projectName"
        style="margin-bottom: 30px"
        @click="onPostClick"
      ></MyRelation>
      <div class="title">人员信息</div>
      <a-empty v-if="!personData.length" />
      <template v-else>
        <div class="person-item" v-for="(el, i) in personData" :key="i" cla>
          <Person :dataSource="el" @click="onPersonClick" />
        </div>
      </template>

      <PostInfoModal
        v-if="showPostInfoModal"
        ref="postInfoModalRef"
        :positionTypeOptions="positionTypeOptions"
        @close="showPostInfoModal = false"
      />

      <PersonInfoModal v-if="showPersonInfoModal" ref="personInfoModalRef" @close="showPersonInfoModal = false" />
    </div>
  </div>
</template>

<script lang="jsx">
  import MyRelation from '@/components/MyRelation/index.vue'
  import Person from '@/components/MyRelation/personInfo.vue'
  import { getPositionType, getPersonnelPage } from './services.js'
  import { getOptions } from '@/api/common.js'
  import PostInfoModal from './modules/PostInfoModal.vue'
  import PersonInfoModal from './modules/PersonInfoModal.vue'

  export default {
    name: 'OrgDept',
    components: { MyRelation, Person, PostInfoModal, PersonInfoModal },
    props: {
      projectId: {},
      projectName: {},
    },
    data() {
      return {
        showPostInfoModal: false,
        showPersonInfoModal: false,
        positionTypeOptions: [],

        orgData: [],
        personData: [],
      }
    },
    created() {
      getPositionType({ projectId: this.projectId }).then(res => {
        getOptions('positionType').then(resp => {
          this.orgData = resp.data.filter(el => res.data.includes(+el.key))
        })
      })

      getOptions('positionType').then(res => {
        this.positionTypeOptions = res.data
      })

      getPersonnelPage({ projectId: this.projectId, pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
        this.personData = res.data?.data || []
      })
    },
    methods: {
      onPostClick(item) {
        this.showPostInfoModal = true
        this.$nextTick(() => this.$refs.postInfoModalRef.handleDetail({ ...item, projectId: this.projectId }))
      },

      onPersonClick(item) {
        this.showPersonInfoModal = true
        this.$nextTick(() => this.$refs.personInfoModalRef.handleDetail({ ...item, projectId: this.projectId }))
      },
    },
  }
</script>

<style lang="scss" scoped>
  .org-dept {
    background-color: #fff;
    height: 100%;
    overflow: auto;

    .title {
      font-size: 16px;
      font-weight: 700;
      margin-bottom: 12px;
    }

    .person-item {
      display: inline-block;
      margin-right: 30px;
      margin-top: 20px;
    }
  }
</style>
