<template>
  <div class="manage-unit">
    <a-tabs v-model="tabKey" size="small">
      <a-tab-pane key="1" tab="应急预案">
        <EmergencyPlan :projectId="projectId" />
      </a-tab-pane>
      <a-tab-pane key="2" tab="放水预警">
        <WaterproofWarning :projectId="projectId" />
      </a-tab-pane>
      <a-tab-pane key="3" tab="安全应急预警">
        <EmergencyWarning :projectId="projectId" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="jsx">
  import EmergencyPlan from './emergency-plan.vue'
  import WaterproofWarning from './waterproof-warning.vue'
  import EmergencyWarning from './emergency-warning.vue'

  export default {
    name: 'EmergencyManage',
    components: {
      EmergencyPlan,
      WaterproofWarning,
      EmergencyWarning,
    },
    props: {
      projectId: {},
      projectName: {},
    },
    data() {
      return {
        tabKey: '1',
      }
    },
    computed: {},
    watch: {},
    created() {},
    methods: {},
  }
</script>
<style lang="scss" scoped>
  .manage-unit {
    margin: 10px 0;
    width: 100%;
    height: calc(100% - 20px);
    overflow: hidden;
    overflow: auto;
  }

  ::v-deep .ant-tabs-top {
    height: 100%;
  }
  ::v-deep .ant-tabs-content {
    height: calc(100% - 50px) !important;
  }
  ::v-deep .ant-tabs-tabpane {
  }
</style>
