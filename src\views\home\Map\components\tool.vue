<template>
  <div class="map-tool" v-show="attrs.isShow.isShowTool">
    <div
      @click="handleClick('distance')"
      class="b-r-(1px dashed #E5E6EB) pl-0.08rem flex-y-center"
      :style="{ cursor: isDrawing ? 'not-allowed' : 'pointer' }"
    >
      <MyIcon :name="active === 'distance' ? 'tool-distance-active' : 'tool-distance'" class="text-0.18rem" />
      <span class="c-text_md text-0.12rem mx-0.08rem" :style="{ color: active === 'distance' ? primaryColor : '' }">测距</span>
    </div>
    <div
      @click="handleClick('area')"
      class="b-r-(1px dashed #E5E6EB) pl-0.08rem flex-y-center"
      :style="{ cursor: isDrawing ? 'not-allowed' : 'pointer' }"
    >
      <MyIcon :name="active === 'area' ? 'tool-area-active' : 'tool-area'" class="text-0.18rem" />
      <span class="c-text_md text-0.12rem mx-0.08rem" :style="{ color: active === 'area' ? primaryColor : '' }">测面</span>
    </div>
    <div
      @click="handleClick('dot')"
      class="b-r-(1px dashed #E5E6EB) pl-0.08rem flex-y-center"
      :style="{ cursor: isDrawing ? 'not-allowed' : 'pointer' }"
    >
      <MyIcon :name="active === 'dot' ? 'tool-dot-active' : 'tool-dot'" class="text-0.18rem" />
      <span class="c-text_md text-0.12rem mx-0.08rem" :style="{ color: active === 'dot' ? primaryColor : '' }">点坐标</span>
    </div>
    <div
      @click="handleClick('clear')"
      class="pl-0.08rem flex-y-center"
      :style="{ cursor: isDrawing ? 'not-allowed' : 'pointer' }"
    >
      <MyIcon name="tool-clear" class="text-0.18rem" />
      <span class="c-text_md text-0.12rem mx-0.08rem">清除</span>
    </div>
  </div>

  <div class="map-tip" v-if="isDrawing">点击地图绘制，双击完成</div>
</template>
<script setup lang="tsx" name="Tool">
  import mapboxgl from 'mapbox-gl'
  import MeatureTool from '../utils/measureTool.js'
  import pointMark from '@/assets/images/poi-marker-default.png'

  import { appConfig } from '@/constants'

  const primaryColor = appConfig.common.primaryColor

  const attrs = useAttrs()
  let active = $ref(null)
  let meatureTool = $ref(null)
  let markerIns = $ref(null)
  let isDrawing = $ref(false)

  const handleClick = (type: string | null) => {
    if (isDrawing) return

    active = type

    attrs.mapIns.off('click', onMapClick)

    meatureTool?.setDistance()
    meatureTool?.setArea()

    if (type === 'distance') {
      const layerId = String(new Date().getTime())
      if (!meatureTool) {
        meatureTool = new MeatureTool({ map: attrs.mapIns })
      }
      meatureTool.measureDistance(layerId, () => {
        isDrawing = false

        handleClick(null)
      })
      // // 防止函数冲突
      meatureTool.setDistance()
      nextTick(() => (isDrawing = true))
    }

    if (type === 'area') {
      const layerId = String(new Date().getTime())
      if (!meatureTool) {
        meatureTool = new MeatureTool({ map: attrs.mapIns })
      }
      meatureTool.measureArea(layerId, () => {
        isDrawing = false
        handleClick(null)
      })
      // 防止函数冲突
      meatureTool.setArea()
      nextTick(() => (isDrawing = true))
    }

    if (type === 'dot') {
      attrs.mapIns.on('click', onMapClick)
    }

    if (type === 'clear') {
      if (meatureTool) {
        meatureTool.clearMeasureAll()
      }

      markerIns?.remove()
      active = null
    }
  }

  async function onMapClick(_e) {
    markerIns?.remove()

    const coords = [_e.lngLat.lng, _e.lngLat.lat]

    const popupIns = new mapboxgl.Popup({
      closeButton: false,
      closeOnClick: false,
      offset: [0, -24],
      anchor: 'bottom',
      maxWidth: 'none',
    }).setHTML(`<div style="position: relative; padding: 12px">
					<div id="marker-poi" style="position:absolute;right:4px;top:4px;cursor:pointer;font-size:11px;width:12px;height:12px;display:flex;justify-content:center;align-items:center;border-radius:50%;:hover{background:#CCC;}">x</div>
					${coords}
					</div>`)

    const el = document.createElement('div')
    el.innerHTML = `
						<div style="cursor:default;width:16px; height:21px; border-radius:4px; background-size:100%;  background-image:url('${pointMark}');">
						</div>`

    markerIns = new mapboxgl.Marker({
      element: el,
      anchor: 'bottom-left',
      offset: [-8, 0],
    })

    markerIns.setLngLat(coords).setPopup(popupIns).addTo(attrs.mapIns)
    markerIns.togglePopup()

    nextTick(() => {
      setTimeout(() => {
        document.getElementById('marker-poi')?.addEventListener('click', () => {
          markerIns.remove()
          active = null
          attrs.mapIns.off('click', onMapClick)
        })
      }, 10)
    })
  }
</script>
<style lang="scss" scoped>
  .map-tool {
    position: absolute;
    top: 0.2rem;
    right: 0.2rem;
    z-index: 999;
    width: 3.16rem;
    height: 0.45rem;
    padding: 0.12rem 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    border-radius: 0.04rem;
    box-shadow: 0px 0.04rem 0.1rem 0px rgba(0, 0, 0, 0.1);
  }
  .map-tip {
    position: absolute;
    top: 0.2rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 999;
    height: 0.45rem;
    padding: 0.12rem;
    font-size: 0.14rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    border-radius: 0.04rem;
    box-shadow: 0px 0.04rem 0.1rem 0px rgba(0, 0, 0, 0.1);
  }
</style>

<style lang="scss"></style>
