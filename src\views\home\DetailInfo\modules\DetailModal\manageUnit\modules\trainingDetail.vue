<template>
  <!-- 增加修改 -->
  <ant-modal :visible="open" :modal-title="modalTitle" modalWidth="700" @cancel="cancel" modalHeight="500">
    <div slot="content">
      <a-row :gutter="32">
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">培训名称：</label>
            <span class="common-value-text">{{ data.trainingName }}</span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">完成情况：</label>
            <span class="common-value-text">
              {{ statusImplementationOptions.find(el => el.value == data.statusImplementation)?.label }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">计划经费(万元)：</label>
            <span class="common-value-text">{{ data.planExpense }}</span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">实际经费(万元)：</label>
            <span class="common-value-text">{{ data.actualExpense }}</span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">培训时间：</label>
            <span class="common-value-text">{{ data.trainingTime }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">所属工程：</label>
            <span class="common-value-text">{{ data.projectName }}</span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="item">
            <label class="common-label-text">培训内容：</label>
            <span class="common-value-text" :title="data.trainingContent">{{ data.trainingContent }}</span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="item">
            <label class="common-label-text" :title="data.trainingContent">培训计划：</label>
            <span class="common-value-text">{{ data.trainingPlan }}</span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div>
            <label class="common-label-text">证书附件：</label>
            <div class="file-item" v-for="(el, i) in data.proofAttaches" :key="i" @click="() => downLoad(el.attachUrl)">
              <a-icon type="paper-clip" />
              <div class="file-name" style="margin-left: 5px">{{ el.attachName }}</div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getTraining } from '../services'
  import moment from 'moment'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'DetailModal',
    props: [],
    components: { AntModal },
    data() {
      return {
        open: false,
        modalTitle: '',
        statusImplementationOptions: [
          { label: '完成', value: 1 },
          { label: '未完成', value: 2 },
        ],
        data: {},
      }
    },
    filters: {},
    created() {},
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      /** 查看按钮操作 */
      handleDetail(record) {
        this.open = true
        this.modalTitle = '查看'

        getTraining({ trainingId: record.trainingId }).then(res => {
          this.data = res.data
        })
      },
      downLoad(url) {
        window.open(url)
      },
    },
  }
</script>

<style lang="less" scoped>
  @import url('~@/global.less');

  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .file-item {
    display: flex;
    align-items: center;
    line-height: 25px;
  }

  .file-name {
    color: @primary-color;
    cursor: pointer;
    margin-right: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
</style>
