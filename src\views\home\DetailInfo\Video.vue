<template>
  <div v-if="state.isShow" class="my-video-modal" v-drag="'.video-drag-area'" :style="{ zIndex: videoIndex }" @click="onClick">
    <header class="py-16px px-24px flex-center-between video-drag-area" id="video-drag-area">
      <div class="title text-20px font-[SourceHanSansCN-Medium]">{{ state.title }}</div>
      <MyIcon class="i-material-symbols:close-small-outline-rounded text-28px cursor-pointer" @click="state.isShow = false" />
    </header>
    <div class="size-full px-16px pb-16px flex-1 overflow-hidden">
      <div class="video-content">
        <div class="tabs">
          <div
            class="tab-item"
            v-for="(item, index) in state.videoList"
            :key="index"
            @click="onClickCamera(item)"
            :class="{ active: state.channelCode == item.channelCode }"
          >
            {{ item.cameraName }}
          </div>
        </div>
        <div class="video-box">
          <videoEasyPlayer v-if="state.channelCode" :channel="state.channelCode" :device="state.deviceCode" />
          <img class="img" v-else src="@/assets/images/video_default_img.png" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx" name="Video">
  import { useUserStore } from '@/store'
  import { getVideoById } from '../services'
  import videoEasyPlayer from '@/components/videoEasyPlayer/index.vue'
  import { initDrag } from '@/components/MyModal/utils'

  const attrs = useAttrs()

  const userStore = useUserStore()

  const monitorIndex = defineModel('monitorIndex')
  const videoIndex = defineModel('videoIndex')

  const state = reactive({
    isShow: false,
    title: '',
    videoList: [],
    menuType: '',
    channelCode: undefined,
    deviceCode: undefined,
  })

  const onClick = () => {
    monitorIndex.value = 999
    videoIndex.value = 9999
  }

  async function openModal(item) {
    state.isShow = true
    state.title = `${item.objectName}`
    state.menuType = item.objectType

    monitorIndex.value = 999
    videoIndex.value = 9999

    // nextTick(() => {
    //   // const initDragFn = new Function('return ' + initDrag.toString())()
    //   initDrag(document.querySelector('.video-drag-area'), document.querySelector('.my-video-modal'))
    // })

    getVideoById({ objectId: item.objectId, objectType: item.objectType }).then(res => {
      state.videoList = res?.data
      state.channelCode = res?.data[0]?.channelCode
      getIdByType(res.data[0])
    })
  }
  defineExpose({ openModal })

  const onClickCamera = (row: object) => {
    state.channelCode = row?.channelCode
    getIdByType(row)
  }
  const getIdByType = (row: object) => {
    state.deviceCode = row.deviceCode
  }
</script>
<style lang="scss" scoped>
  .my-video-modal {
    width: 40vw;
    height: 35vh;
    position: fixed;
    left: calc(50vw - 20vw);
    bottom: 0;
    background-color: #ffffff;
    border-radius: 16px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow:
      0 6px 16px -9px rgba(0, 0, 0, 0.08),
      0 9px 28px 0 rgba(0, 0, 0, 0.05),
      0 12px 48px 16px rgba(0, 0, 0, 0.03);
  }

  :deep(.n-card > .n-card__content) {
    padding: 0 !important;
  }
  .video-content {
    overflow: auto;
    display: flex;
    height: 100%;
    .tabs {
      padding-top: 0.14rem;
      overflow-x: auto;
      background: #f7f8fa;
      .tab-item {
        color: #1d2129;
        font-family: PingFang SC;
        font-size: 0.14rem;
        padding-left: 0.14rem;
        line-height: 0.32rem;
        padding-right: 0.14rem;
        cursor: pointer;
        &.active {
          color: #165dff;
          background: #fff;
        }
      }
    }
    .video-box {
      flex: 1;
      padding-left: 0.14rem;
      display: flex;
      align-items: center;
      img {
        display: block;
        margin: auto;
      }
    }
  }
</style>
