import request from '@/utils/request'

// 年度资金-列表分页查询
export function getCapitalPage(data) {
  return request({
    url: '/prjstd/capital/page',
    method: 'post',
    data
  })
}
// 增加
export function addCapital(data) {
  return request({
    url: '/prjstd/capital/add',
    method: 'post',
    data
  })
}
// 详情
export function getCapitalById(params) {
  return request({
    url: '/prjstd/capital/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
// 更新
export function editCapital(data) {
  return request({
    url: '/prjstd/capital/update',
    method: 'post',
    data
  })
}
// 删除
export function deleteCapital(params) {
  return request({
    url: '/prjstd/capital/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
