import Layout from '@/layout/index.vue'

const routes = [
  {
    path: '/',
    name: 'root',
    component: Layout,
    children: [
      {
        path: '/',
        name: 'Home',
        component: () => import('@/views/home/<USER>'),
        meta: {
          title: '首页',
          requiresAuth: true,
        },
      },

      {
        path: '/login',
        name: 'login',
        component: () => import('@/views/login/index.vue'),
      },
      {
        path: '/403',
        name: '403',
        component: () => import('@/views/error-pages/403.vue'),
      },
      {
        path: '/404',
        name: '404',
        component: () => import('@/views/error-pages/404.vue'),
      },
      {
        path: '/500',
        name: '500',
        component: () => import('@/views/error-pages/500.vue'),
      },
      {
        path: '/demo',
        component: () => import('@/views/demo/index.vue'),
      },
    ],
  },
]

export default routes
