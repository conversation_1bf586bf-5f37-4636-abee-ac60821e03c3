export const useModal = () => {
  const modalRef = ref(null)

  const title = computed({
    get() {
      return modalOptions.value.title
    },
    set(v) {
      modalRef.value.title = v
    },
  })

  const loading = computed({
    get() {
      return modalRef.value?.loading
    },
    set(v) {
      modalRef.value.loading = v
    },
  })

  const okLoading = computed({
    get() {
      return modalRef.value?.okLoading
    },
    set(v) {
      modalRef.value.okLoading = v
    },
  })
  return [modalRef, okLoading, loading, title]
}
