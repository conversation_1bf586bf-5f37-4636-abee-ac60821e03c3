<template>
  <div class="map">
    <div class="track-control">
      <a-button @click="startClick">开始</a-button>
      <a-button @click="pauseClick">暂停</a-button>
      <a-button @click="endClick">停止</a-button>
      <!-- <a-button @click="removeClick">移除</a-button> -->
    </div>

    <MapBox :options="{}" @onMapMounted="onMapMounted" @onMapStyleLoad="onMapStyleLoad" />

    <div style="position: absolute; right: 16px; bottom: 16px">
      <MapStyle v-if="!!mapIns" :mapIns="mapIns" ref="mapStyleRef" />
    </div>
  </div>
</template>

<script lang="jsx">
  import MapBox from '@/components/MapBox/index.vue'
  import * as turf from '@turf/turf'
  import { dealAllPoint, mapBound, mapBoundGeo } from '@/utils/mapBounds.js'
  import { clearSourceAndLayer } from '@/utils/mapUtils'
  import currentImg from '@/assets/images/poi-current.png'
  import RouteReplay from '@/utils/RouteReplay'
  import MapStyle from '@/components/MapBox/MapStyle.vue'

  export default {
    name: 'Map',
    components: { MapBox, MapStyle },
    props: {
      dataSource: { default: () => [] },
      rowInfo: {
        default: null,
      },
    },
    data() {
      return {
        mapIns: null,
        routeReplayIns: null,
        trackData: null,
      }
    },
    computed: {},
    watch: {
      dataSource: {
        handler(newVal, oldVal) {
          this.dealLayers(newVal)
        },
        deep: true,
      },
      rowInfo: {
        handler(newVal, oldVal) {
          this.activeArea()
        },
        deep: true,
      },
    },
    created() {},
    methods: {
      onMapMounted(mapIns) {
        if (this.mapIns) return

        this.mapIns = mapIns
        this.dealLayers(this.dataSource)
      },

      onMapStyleLoad(mapIns) {
        if (this.trackData) {
          this.routeReplayIns = new RouteReplay(mapIns, this.trackData, currentImg)
        }
        this.dealLayers(this.dataSource)
      },

      startClick() {
        this.routeReplayIns.start()
      },
      pauseClick() {
        this.routeReplayIns.pause()
      },
      endClick() {
        this.routeReplayIns.end()
      },
      removeClick() {
        this.routeReplayIns.remove()
      },

      onTrackReplay(trackData) {
        if (this.routeReplayIns) {
          this.removeClick()
        }

        this.trackData = trackData
        this.routeReplayIns = new RouteReplay(this.mapIns, trackData, currentImg)

        this.$nextTick(() => {
          setTimeout(() => {
            this.startClick()
          }, 500)
        })
      },

      // 图层
      dealLayers(list) {
        if (!this.mapIns) return
        if (!list?.length) return

        if (this.mapIns.getSource('all-area')) {
          clearSourceAndLayer(this.mapIns, ['all-area'], ['all-area', 'all-point'])
        }

        let allGeoJson = { type: 'FeatureCollection', features: [] }
        list
          .filter(el => !!el.lineRange)
          .forEach(ele => {
            allGeoJson.features.push.apply(allGeoJson.features, ele.lineRange.features)

            allGeoJson.features.push({
              type: 'Feature',
              properties: { ...ele, lineRange: null },
              geometry: turf.centroid(ele.lineRange).geometry,
            })
          })

        this.mapIns.addSource('all-area', { type: 'geojson', data: allGeoJson })

        this.allGeoJson = allGeoJson
        mapBoundGeo(this.allGeoJson, this.mapIns, { top: 100, bottom: 100, left: 100, right: 100 })

        this.mapIns.addLayer({
          id: 'all-area',
          type: 'fill',
          source: 'all-area',
          paint: {
            'fill-color': '#00FF16',
            'fill-opacity': 0.35,
          },
          filter: ['==', '$type', 'Polygon'],
        })

        this.mapIns.addLayer({
          id: 'all-point',
          type: 'symbol',
          source: 'all-area',
          layout: {
            'text-size': 12,
            'text-field': ['get', 'lineName'],
            // 'text-offset': [0, 1.25],
            'text-anchor': 'center',
          },
          filter: ['==', '$type', 'Point'],
        })
      },

      activeArea() {
        if (!this.mapIns) return
        if (!this.rowInfo) {
          mapBoundGeo(this.allGeoJson, this.mapIns)
          if (this.mapIns.getSource('active-area')) {
            clearSourceAndLayer(this.mapIns, ['active-area'], ['active-area'])
          }
          return
        }
        if (this.mapIns.getSource('active-area')) {
          clearSourceAndLayer(this.mapIns, ['active-area'], ['active-area'])
        }
        if (!this.rowInfo.lineRange) return
        this.mapIns.addSource('active-area', { type: 'geojson', data: this.rowInfo.lineRange })
        this.mapIns.addLayer({
          id: 'active-area',
          type: 'line',
          source: 'active-area',
          paint: {
            'line-color': '#00C911',
            'line-width': 2,
          },
        })

        mapBoundGeo(this.rowInfo.lineRange, this.mapIns, { top: 150, bottom: 150, left: 150, right: 150 })
      },

      // drawMarks(list) {
      //   if (!this.mapIns) return
      //   if (!list?.length) return

      //   let allGeoJson = { type: 'FeatureCollection', features: [] }

      //   list.forEach(ele => {
      //     if (ele.latitude && ele.longitude) {
      //       allGeoJson.features.push({
      //         type: 'Feature',
      //         properties: ele,
      //         geometry: {
      //           type: 'Point',
      //           coordinates: gcoord.transform([+ele.longitude, +ele.latitude], gcoord.GCJ02, gcoord.WGS84)
      //         }
      //       })
      //     }
      //   })

      //   this.mapIns.addSource('all-makers', { type: 'geojson', data: allGeoJson })

      // }
    },
  }
</script>

<style lang="less" scoped>
  @import url('~@/global.less');
  .map {
    width: 100%;
    height: 100%;
    position: relative;
    .search {
      position: absolute;
      left: 20px;
      top: 20px;
      z-index: 1;
    }

    .track-control {
      position: absolute;
      left: 10px;
      top: 10px;
      z-index: 1;
      button:nth-child(n) {
        margin-right: 10px;
      }
    }

    .legend {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 6px;
      position: absolute;
      left: 10px;
      bottom: 10px;
      padding: 8px 12px;
      .legend-item {
        display: flex;
        align-items: center;

        font-size: 14px;
        color: #ffffff;
        line-height: 30px;
        text-shadow: 0px 0px 6px #000000;
        > img {
          width: 20px;
          height: 24px;
          margin-right: 8px;
        }
      }
    }
  }

  ::v-deep .mapboxgl-ctrl-top-left {
    display: none;
  }
  ::v-deep .mapboxgl-ctrl-bottom-right {
    // display: none;
    bottom: 80px;
  }
</style>
