<template>
  <ant-modal :visible="open" :modal-title="formTitle" modalWidth="600" @cancel="cancel" modalHeight="500">
    <div slot="content">
      <a-row class="form-row" :gutter="32">
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">年份：</label>
            <span class="common-value-text">{{ yearFormat(form.year) }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">通知对象：</label>
            <span class="common-value-text">{{ form.noticePeople }}</span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">通知时间：</label>
            <span class="common-value-text">{{ form.noticeTime }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">通知发出人：</label>
            <span class="common-value-text">{{ form.noticeOut }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">反馈时间：</label>
            <span class="common-value-text">{{ form.feedbackTime }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">反馈人姓名：</label>
            <span class="common-value-text">{{ form.feedbackName }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">是否完成放水预警：</label>
            <span class="common-value-text">{{ form.isReleaseWater == 0 ? '否' : '是' }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">所属工程：</label>
            <span class="common-value-text">{{ form.projectName }}</span>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24">
          <div class="item">
            <label class="common-label-text">反馈情况：</label>
            <span class="common-value-text">{{ form.feedbackConcent }}</span>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24">
          <div class="item">
            <label class="common-label-text">影响泄洪情况说明：</label>
            <span class="common-value-text">{{ form.description }}</span>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div>
            <label class="common-label-text">附件：</label>
            <div
              class="file-item"
              v-for="(el, i) in form.positionAttaches"
              :key="i"
              @click="() => downLoad(el.attachUrl)"
            >
              <a-icon type="paper-clip" />
              <div class="file-name" style="margin-left: 5px" :title="el.attachName">{{ el.attachName }}</div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getReleaseById } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import moment from 'moment'

  export default {
    name: 'FormDetails',
    components: { AntModal },
    props: ['projectOptions', 'releaseWaterOptions'],
    data() {
      return {
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        formTitle: '',
        form: {
          description: '',
          positionAttaches: [],
          feedbackConcent: '',
          feedbackName: '',
          feedbackTime: '',
          isReleaseWater: null,
          noticeOut: '',
          noticePeople: '',
          noticeTime: '',
          projectId: null,
          releaseWaterId: null,
          year: null,
        },
        open: false,
        rules: {
          eventDescription: [{ required: true, message: '应急事件说明不能为空', trigger: 'blur' }],
          year: [{ required: true, message: '年份不能为空', trigger: 'change' }],
          levelResponse: [{ required: true, message: '响应等级不能为空', trigger: 'change' }],
          startTime: [{ required: true, message: '应急启动时间不能为空', trigger: 'change' }],
          endTime: [{ required: true, message: '结束时间不能为空', trigger: 'change' }],
          projectId: [{ required: true, message: '所属工程不能为空', trigger: 'change' }],
        }
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 响应等级格式化
      levelFormat(value) {
        if (value) {
          return this.releaseWaterOptions.find(item => item.key == value)?.value
        } else {
          return ''
        }
      },
      // 年份格式化
      yearFormat(value) {
        if (value) {
          return moment(value).format('YYYY')
        } else {
          return ''
        }
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
          this.planDate = []
          this.taskDate = []
        }
      },

      /** 新增按钮操作 */
      details(row) {
        this.open = true
        if (row != undefined) {
          this.formTitle = '查看'
          getReleaseById({ releaseWaterId: row.releaseWaterId }).then(res => {
            if (res.code == 200) {
              this.form = res.data
              // this.form.isReleaseWater = String(this.form.isReleaseWater)
              this.form.year = moment(`${res.data.year}-01-01`)
              //附件显示
            }
          })
        }
      }
    }
  }
</script>
<style lang="less" scoped>
  @import url('~@/global.less');

  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .file-item {
    display: flex;
    align-items: center;
    line-height: 25px;
  }

  .file-name {
    color: @primary-color;
    cursor: pointer;
    margin-right: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
</style>
