<template>
  <div class="manage-unit">
    <a-tabs v-model="tabKey" size="small">
      <a-tab-pane key="1" tab="隐患信息"></a-tab-pane>
      <a-tab-pane key="2" tab="维养任务">
        <MaintenanceTask :projectId="projectId" />
      </a-tab-pane>
      <a-tab-pane key="3" tab="物业管理">
        <propertyManage :projectId="projectId" />
      </a-tab-pane>
      <a-tab-pane key="4" tab="年度资金">
        <annualFunds :projectId="projectId" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="jsx">
  import MaintenanceTask from './maintenance-tasks/index.vue' //维养任务
  import propertyManage from './property-manage/index.vue' //物业管理
  import annualFunds from './annual-funds/index.vue' //年底资金

  export default {
    name: 'maintenanceUpkeep',
    props: {
      projectId: {},
      projectName: {},
    },
    components: {
      MaintenanceTask,
      propertyManage,
      annualFunds,
    },
    data() {
      return {
        tabKey: '2',
      }
    },
    computed: {},
    watch: {},
    created() {},
    methods: {},
  }
</script>
<style lang="less" scoped>
  .manage-unit {
    margin: 10px 0;
    width: 100%;
    height: calc(100% - 20px);
    overflow: hidden;
    overflow: auto;
  }

  ::v-deep .ant-tabs-top {
    height: 100%;
  }
  ::v-deep .ant-tabs-content {
    height: calc(100% - 50px) !important;
  }
  ::v-deep .ant-tabs-tabpane {
  }
</style>
