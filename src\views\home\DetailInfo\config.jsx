export const configs = {
  RL001: {
    objectCategoryName: '流域',
    api: riverSystemId =>
      request({
        url: '/base/bas/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { riverSystemId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '流域代码', key: 'riverSystemCode' },
      { label: '流域名称', key: 'riverSystemName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '左下角经度(°)', key: 'lowLeftLong' },
      { label: '左下角维度(°)', key: 'lowLeftLat' },
      { label: '右上角经度(°)', key: 'upRightLong' },
      { label: '右上角维度(°)', key: 'upRightLat' },
      {
        label: '跨界类型',
        key: 'crOverType',
        optionsField: 'crOverType',
        options: [],
      },
    ],
  },
  RL002: {
    objectCategoryName: '河流',
    api: riverSystemId =>
      request({
        url: '/base/rv/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { riverSystemId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '河流代码', key: 'riverSystemCode' },
      { label: '河流名称', key: 'riverSystemName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '左下角经度(°)', key: 'rvMouLong' },
      { label: '左下角维度(°)', key: 'rvMouLat' },
      { label: '右上角经度(°)', key: 'rvSourLong' },
      { label: '右上角维度(°)', key: 'rvSourLat' },
      {
        label: '跨界类型',
        key: 'crOverType',
        optionsField: 'crOverType',
        options: [],
      },
      { label: '流经地区', key: 'flowArea' },
      { label: '河口所在位置', key: 'rvMouLoc' },
      { label: '河源所在位置', key: 'rvSourLoc' },
    ],
  },
  RL003: {
    objectCategoryName: '湖泊',
    api: riverSystemId =>
      request({
        url: '/base/lk/get',
        method: 'post',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        data: { riverSystemId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '湖泊代码', key: 'riverSystemCode' },
      { label: '湖泊名称', key: 'riverSystemName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '左下角经度(°)', key: 'lowLeftLong' },
      { label: '左下角维度(°)', key: 'lowLeftLat' },
      { label: '右上角经度(°)', key: 'upRightLong' },
      { label: '右上角维度(°)', key: 'upRightLat' },
      {
        label: '跨界类型',
        key: 'crOverType',
        optionsField: 'crOverType',
        options: [],
      },
      { label: '湖泊所在位置', key: 'lkLoc' },
    ],
  },
  HP018: {
    objectCategoryName: '圩区',
    api: projectId =>
      request({
        url: '/base/pold/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '流域代码', key: 'projectCode' },
      { label: '流域名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '左下角经度(°)', key: 'lowLeftLong' },
      { label: '左下角维度(°)', key: 'lowLeftLat' },
      { label: '右上角经度(°)', key: 'upRightLong' },
      { label: '右上角维度(°)', key: 'upRightLat' },
      { label: '圩垸所在位置', key: 'location' },
    ],
  },
  HP034: {
    objectCategoryName: '闸站',
    api: projectId =>
      request({
        url: '/base/gate/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '闸站代码', key: 'projectCode' },
      { label: '闸站名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '闸站经度(°)', key: 'longitude' },
      { label: '闸站维度(°)', key: 'latitude' },
      { label: '闸站所在位置', key: 'location' },
    ],
  },
  HP010: {
    objectCategoryName: '泵站',
    api: projectId =>
      request({
        url: '/base/pust/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '泵站代码', key: 'projectCode' },
      { label: '泵站名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '泵站经度(°)', key: 'longitude' },
      { label: '泵站维度(°)', key: 'latitude' },
      { label: '泵站所在位置', key: 'location' },
    ],
  },
  HP007: {
    objectCategoryName: '水闸',
    api: projectId =>
      request({
        url: '/base/waga/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '水闸代码', key: 'projectCode' },
      { label: '水闸名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '起点经度(°)', key: 'startLong' },
      { label: '起点纬度(°)', key: 'startLat' },
      { label: '终点经度(°)', key: 'endLong' },
      { label: '终点纬度(°)', key: 'endLat' },
      { label: '水闸所在位置', key: 'location' },
    ],
  },
  HP017: {
    objectCategoryName: '堤防',
    api: projectId =>
      request({
        url: '/base/dike/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '堤防代码', key: 'projectCode' },
      { label: '堤防名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '起点经度(°)', key: 'startLong' },
      { label: '起点纬度(°)', key: 'startLat' },
      { label: '终点经度(°)', key: 'endLong' },
      { label: '终点纬度(°)', key: 'endLat' },
      { label: '起点所在位置', key: 'startLoc' },
      { label: '终点所在位置', key: 'endLoc' },
    ],
  },
  HP003: {
    objectCategoryName: '水电站',
    api: projectId =>
      request({
        url: '/base/hyst/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '水电站代码', key: 'projectCode' },
      { label: '水电站名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '水电站经度(°)', key: 'longitude' },
      { label: '水电站纬度(°)', key: 'latitude' },
      { label: '所在位置', key: 'location' },
    ],
  },
  HP002: {
    objectCategoryName: '水库大坝',
    api: projectId =>
      request({
        url: '/base/dam/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '水电站代码', key: 'projectCode' },
      { label: '水电站名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '起点经度(°)', key: 'startLong' },
      { label: '起点纬度(°)', key: 'startLat' },
      { label: '终点经度(°)', key: 'endLong' },
      { label: '终点纬度(°)', key: 'endLat' },
      { label: '所在位置', key: 'location' },
    ],
  },
  HP004: {
    objectCategoryName: '灌区',
    api: projectId =>
      request({
        url: '/base/irr/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '灌区代码', key: 'projectCode' },
      { label: '灌区名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '左下角经度(°)', key: 'lowLeftLong' },
      { label: '左下角维度(°)', key: 'lowLeftLat' },
      { label: '右上角经度(°)', key: 'upRightLong' },
      { label: '右上角维度(°)', key: 'upRightLat' },
      {
        label: '跨界类型',
        key: 'crOverType',
        optionsField: 'crOverType',
        options: [],
      },
      { label: '灌区范围', key: 'location' },
    ],
  },
  HP005: {
    objectCategoryName: '渠(沟)道',
    api: projectId =>
      request({
        url: '/base/chan/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '渠(沟)道代码', key: 'projectCode' },
      { label: '渠(沟)道名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '起点经度(°)', key: 'startLong' },
      { label: '起点纬度(°)', key: 'startLat' },
      { label: '终点经度(°)', key: 'endLong' },
      { label: '终点纬度(°)', key: 'endLat' },
      { label: '渠(沟)道所在位置', key: 'location' },
    ],
  },
  HP001: {
    objectCategoryName: '水库',
    api: projectId =>
      request({
        url: '/base/res/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '水库代码', key: 'projectCode' },
      { label: '水库名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '左下角经度(°)', key: 'lowLeftLong' },
      { label: '左下角维度(°)', key: 'lowLeftLat' },
      { label: '右上角经度(°)', key: 'upRightLong' },
      { label: '右上角维度(°)', key: 'upRightLat' },
      { label: '水库所在位置', key: 'location' },
    ],
  },
  HP006: {
    objectCategoryName: '取水井',
    api: projectId =>
      request({
        url: '/base/well/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '取水井代码', key: 'projectCode' },
      { label: '取水井名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '取水井经度(°)', key: 'longitude' },
      { label: '取水井维度(°)', key: 'latitude' },
      { label: '取水井所在位置', key: 'location' },
    ],
  },
  HP012: {
    objectCategoryName: '引调水',
    api: projectId =>
      request({
        url: '/base/wadl/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '引调水代码', key: 'projectCode' },
      { label: '引调水名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '起点经度(°)', key: 'startLong' },
      { label: '起点纬度(°)', key: 'startLat' },
      { label: '终点经度(°)', key: 'endLong' },
      { label: '终点纬度(°)', key: 'endLat' },
      { label: '起点所在位置', key: 'startLoc' },
      { label: '终点所在位置', key: 'endLoc' },
    ],
  },
  HP013: {
    objectCategoryName: '农村供水',
    api: projectId =>
      request({
        url: '/base/cws/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '农村供水工程代码', key: 'projectCode' },
      { label: '农村供水工程名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '农村供水工程经度(°)', key: 'longitude' },
      { label: '农村供水工程维度(°)', key: 'latitude' },
      { label: '农村供水工程所在位置', key: 'location' },
    ],
  },
  HP016: {
    objectCategoryName: '蓄滞洪区',
    api: projectId =>
      request({
        url: '/base/fsda/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '蓄滞洪区代码', key: 'projectCode' },
      { label: '蓄滞洪区名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '左下角经度(°)', key: 'lowLeftLong' },
      { label: '左下角维度(°)', key: 'lowLeftLat' },
      { label: '右上角经度(°)', key: 'upRightLong' },
      { label: '右上角维度(°)', key: 'upRightLat' },
      { label: '蓄滞洪区所在位置', key: 'location' },
    ],
  },
  HP015: {
    objectCategoryName: '塘坝',
    api: projectId =>
      request({
        url: '/base/pond/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '塘坝代码', key: 'projectCode' },
      { label: '塘坝名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '左下角经度(°)', key: 'lowLeftLong' },
      { label: '左下角维度(°)', key: 'lowLeftLat' },
      { label: '右上角经度(°)', key: 'upRightLong' },
      { label: '右上角维度(°)', key: 'upRightLat' },
      { label: '塘坝所在位置', key: 'location' },
    ],
  },
  HP008: {
    objectCategoryName: '渡槽',
    api: projectId =>
      request({
        url: '/base/flum/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '渡槽代码', key: 'projectCode' },
      { label: '渡槽名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '起点经度(°)', key: 'startLong' },
      { label: '起点纬度(°)', key: 'startLat' },
      { label: '终点经度(°)', key: 'endLong' },
      { label: '终点纬度(°)', key: 'endLat' },
      { label: '渡槽所在位置', key: 'location' },
    ],
  },
  HP009: {
    objectCategoryName: '倒虹吸',
    api: projectId =>
      request({
        url: '/base/insi/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '倒虹吸代码', key: 'projectCode' },
      { label: '倒虹吸名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '起点经度(°)', key: 'startLong' },
      { label: '起点纬度(°)', key: 'startLat' },
      { label: '终点经度(°)', key: 'endLong' },
      { label: '终点纬度(°)', key: 'endLat' },
      { label: '倒虹吸所在位置', key: 'location' },
    ],
  },
  HP011: {
    objectCategoryName: '涵洞',
    api: projectId =>
      request({
        url: '/base/culv/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '涵洞代码', key: 'projectCode' },
      { label: '涵洞名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '涵洞经度(°)', key: 'longitude' },
      { label: '涵洞维度(°)', key: 'latitude' },
      { label: '涵洞所在位置', key: 'location' },
    ],
  },
  HP014: {
    objectCategoryName: '窖池',
    api: projectId =>
      request({
        url: '/base/pit/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '窖池代码', key: 'projectCode' },
      { label: '窖池名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '窖池经度(°)', key: 'longitude' },
      { label: '窖池维度(°)', key: 'latitude' },
      { label: '窖池所在位置', key: 'location' },
    ],
  },
  HP019: {
    objectCategoryName: '治河工程',
    api: projectId =>
      request({
        url: '/base/grpj/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '治河工程代码', key: 'projectCode' },
      { label: '治河工程名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '治河工程几何中心点经度(°)', key: 'longitude' },
      { label: '治河工程几何中心点维度(°)', key: 'latitude' },
      { label: '治河工程所在位置', key: 'location' },
    ],
  },
  HP020: {
    objectCategoryName: '淤地坝',
    api: projectId =>
      request({
        url: '/base/sd/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '淤地坝代码', key: 'projectCode' },
      { label: '淤地坝名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '起点经度(°)', key: 'startLong' },
      { label: '起点纬度(°)', key: 'startLat' },
      { label: '终点经度(°)', key: 'endLong' },
      { label: '终点纬度(°)', key: 'endLat' },
      { label: '淤地坝所在位置', key: 'location' },
    ],
  },
  HP021: {
    objectCategoryName: '橡胶坝',
    api: projectId =>
      request({
        url: '/base/ruda/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '橡胶坝代码', key: 'projectCode' },
      { label: '橡胶坝名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '起点经度(°)', key: 'startLong' },
      { label: '起点纬度(°)', key: 'startLat' },
      { label: '终点经度(°)', key: 'endLong' },
      { label: '终点纬度(°)', key: 'endLat' },
      { label: '橡胶坝所在位置', key: 'location' },
    ],
  },
  EX011: {
    objectCategoryName: '取水口',
    api: otherObjectId =>
      request({
        url: '/base/wain/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { otherObjectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '取水口代码', key: 'otherObjectCode' },
      { label: '取水口名称', key: 'otherObjectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '取水口经度(°)', key: 'longitude' },
      { label: '取水口纬度(°)', key: 'latitude' },
      { label: '取水口所在位置', key: 'location' },
    ],
  },
  EX010: {
    objectCategoryName: '水源地',
    api: otherObjectId =>
      request({
        url: '/base/swhs/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { otherObjectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '水源地代码', key: 'otherObjectCode' },
      { label: '水源地名称', key: 'otherObjectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '水源地经度(°)', key: 'longitude' },
      { label: '水源地纬度(°)', key: 'latitude' },
      { label: '水源地所在位置', key: 'location' },
    ],
  },
  EX001: {
    objectCategoryName: '水资源分区',
    api: otherObjectId =>
      request({
        url: '/base/wrz/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { otherObjectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '水资源分区代码', key: 'otherObjectCode' },
      { label: '水资源分区名称', key: 'otherObjectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '左下角经度(°)', key: 'lowLeftLong' },
      { label: '左下角维度(°)', key: 'lowLeftLat' },
      { label: '右上角经度(°)', key: 'upRightLong' },
      { label: '右上角维度(°)', key: 'upRightLat' },
      { label: '水资源分区范围', key: 'wrzRang' },
    ],
  },
  EX002: {
    objectCategoryName: '水功能区',
    api: otherObjectId =>
      request({
        url: '/base/wfz/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { otherObjectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '水功能区代码', key: 'otherObjectCode' },
      { label: '水功能区名称', key: 'otherObjectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '起始断面经度(°)', key: 'startRiseLong' },
      { label: '起始断面维度(°)', key: 'startRiseLat' },
      { label: '终止断面经度(°)', key: 'endRiseLong' },
      { label: '终止断面维度(°)', key: 'endRiseLat' },
      { label: '水功能区范围', key: 'wfzRang' },
    ],
  },
  EX003: {
    objectCategoryName: '水土保持区划',
    api: otherObjectId =>
      request({
        url: '/base/wscz/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { otherObjectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '水土保持区划代码', key: 'otherObjectCode' },
      { label: '水土保持区划名称', key: 'otherObjectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '左下角经度(°)', key: 'lowLeftLong' },
      { label: '左下角维度(°)', key: 'lowLeftLat' },
      { label: '右上角经度(°)', key: 'upRightLong' },
      { label: '右上角维度(°)', key: 'upRightLat' },
      { label: '水土保持区划范围', key: 'wsczRang' },
    ],
  },
  EX004: {
    objectCategoryName: '河湖管理范围',
    api: otherObjectId =>
      request({
        url: '/base/rlmd/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { otherObjectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '河湖管理范围代码', key: 'otherObjectCode' },
      { label: '河湖管理范围名称', key: 'otherObjectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '左下角经度(°)', key: 'lowLeftLong' },
      { label: '左下角维度(°)', key: 'lowLeftLat' },
      { label: '右上角经度(°)', key: 'upRightLong' },
      { label: '右上角维度(°)', key: 'upRightLat' },
      { label: '河湖管理范围边界', key: 'rlmdRang' },
    ],
  },
  EX005: {
    objectCategoryName: '岸线功能分区',
    api: otherObjectId =>
      request({
        url: '/base/slfz/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { otherObjectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '岸线功能分区代码', key: 'otherObjectCode' },
      { label: '岸线功能分区名称', key: 'otherObjectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '左下角经度(°)', key: 'lowLeftLong' },
      { label: '左下角维度(°)', key: 'lowLeftLat' },
      { label: '右上角经度(°)', key: 'upRightLong' },
      { label: '右上角维度(°)', key: 'upRightLat' },
      { label: '岸线功能分区范围', key: 'slfzRang' },
    ],
  },
  EX006: {
    objectCategoryName: '采砂分区',
    api: otherObjectId =>
      request({
        url: '/base/sep/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { otherObjectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '采砂分区代码', key: 'otherObjectCode' },
      { label: '采砂分区名称', key: 'otherObjectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '左下角经度(°)', key: 'lowLeftLong' },
      { label: '左下角维度(°)', key: 'lowLeftLat' },
      { label: '右上角经度(°)', key: 'upRightLong' },
      { label: '右上角维度(°)', key: 'upRightLat' },
      { label: '采砂分区所在位置', key: 'sepLoc' },
    ],
  },
  EX007: {
    objectCategoryName: '河段',
    api: otherObjectId =>
      request({
        url: '/base/rea/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { otherObjectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '河段代码', key: 'otherObjectCode' },
      { label: '河段名称', key: 'otherObjectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '起点经度(°)', key: 'startLong' },
      { label: '起点纬度(°)', key: 'startLat' },
      { label: '终点经度(°)', key: 'endLong' },
      { label: '终点纬度(°)', key: 'endLat' },
      { label: '起点所在位置', key: 'startLoc' },
      { label: '终点所在位置', key: 'endLoc' },
    ],
  },
  EX008: {
    objectCategoryName: '堤段',
    api: otherObjectId =>
      request({
        url: '/base/disc/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { otherObjectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '堤段代码', key: 'otherObjectCode' },
      { label: '堤段名称', key: 'otherObjectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '起点经度(°)', key: 'startLong' },
      { label: '起点纬度(°)', key: 'startLat' },
      { label: '终点经度(°)', key: 'endLong' },
      { label: '终点纬度(°)', key: 'endLat' },
      { label: '起点所在位置', key: 'startLoc' },
      { label: '终点所在位置', key: 'endLoc' },
    ],
  },
  EX009: {
    objectCategoryName: '险工险段',
    api: otherObjectId =>
      request({
        url: '/base/dpds/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { otherObjectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '险工险段代码', key: 'otherObjectCode' },
      { label: '险工险段名称', key: 'otherObjectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '险工险段几何中心经度(°)', key: 'longitude' },
      { label: '险工险段几何中心纬度(°)', key: 'latitude' },
      { label: '险工险段所在位置', key: 'dpdsLoc' },
    ],
  },
  EX012: {
    objectCategoryName: '退排水口',
    api: otherObjectId =>
      request({
        url: '/base/pdo/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { otherObjectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '退排水口代码', key: 'otherObjectCode' },
      { label: '退排水口名称', key: 'otherObjectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '退排水口经度(°)', key: 'longitude' },
      { label: '退排水口纬度(°)', key: 'latitude' },
      { label: '退排水口所在位置', key: 'location' },
    ],
  },
  EX013: {
    objectCategoryName: '取用水户',
    api: otherObjectId =>
      request({
        url: '/base/wiu/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { otherObjectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '取用水户代码', key: 'otherObjectCode' },
      { label: '取用水户名称', key: 'otherObjectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '取用水户经度(°)', key: 'longitude' },
      { label: '取用水户纬度(°)', key: 'latitude' },
      { label: '取用水户所在位置', key: 'location' },
    ],
  },
  MS001: {
    objectCategoryName: '水文监测站',
    api: siteId =>
      request({
        url: '/base/st/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { siteId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '水文监测站代码', key: 'siteCode' },
      { label: '水文监测站名称', key: 'siteName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '水文监测站经度(°)', key: 'longitude' },
      { label: '水文监测站纬度(°)', key: 'latitude' },
      {
        label: '水文监测站类型',
        key: 'stType',
        optionsField: 'stType',
        options: [],
      },
      { label: '水文监测站站址', key: 'location' },
    ],
  },
  MS003: {
    objectCategoryName: '供(取)水量',
    api: siteId =>
      request({
        url: '/base/wvst/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { siteId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '供(取)水量监测站代码', key: 'siteCode' },
      { label: '供(取)水量监测站名称', key: 'siteName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '供(取)水量监测站经度(°)', key: 'longitude' },
      { label: '供(取)水量监测站纬度(°)', key: 'latitude' },
      {
        label: '供(取)水量监测站类型',
        key: 'wvstType',
        optionsField: 'wvstType',
        options: [],
      },
      { label: '供(取)水量监测站站址', key: 'location' },
    ],
  },
  MS002: {
    objectCategoryName: '水土保持监测站',
    api: siteId =>
      request({
        url: '/base/wsst/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { siteId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '水土保持监测站代码', key: 'siteCode' },
      { label: '水土保持监测站名称', key: 'siteName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '水土保持监测站经度(°)', key: 'longitude' },
      { label: '水土保持监测站纬度(°)', key: 'latitude' },
      {
        label: '水土保持监测站类型',
        key: 'wsstType',
        optionsField: 'wsstType',
        options: [],
      },
      { label: '水土保持监测站站址', key: 'location' },
    ],
  },
  HP000: {
    objectCategoryName: '骨干工程',
    api: projectId =>
      request({
        url: '/base/attr/cprj/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '骨干工程代码', key: 'projectCode' },
      { label: '骨干工程名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '骨干工程经度(°)', key: 'longitude' },
      { label: '骨干工程维度(°)', key: 'latitude' },
      { label: '骨干工程所在位置', key: 'location' },
    ],
  },

  HP048: {
    objectCategoryName: '跌水',
    api: projectId =>
      request({
        url: '/base/attr/drop/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '跌水代码', key: 'projectCode' },
      { label: '跌水名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '跌水经度(°)', key: 'longitude' },
      { label: '跌水维度(°)', key: 'latitude' },
      { label: '跌水所在位置', key: 'location' },
    ],
  },
  HP049: {
    objectCategoryName: '陡坡',
    api: projectId =>
      request({
        url: '/base/attr/steep/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '陡坡代码', key: 'projectCode' },
      { label: '陡坡名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '陡坡经度(°)', key: 'longitude' },
      { label: '陡坡维度(°)', key: 'latitude' },
      { label: '陡坡所在位置', key: 'location' },
    ],
  },
  HP050: {
    objectCategoryName: '斗门',
    api: projectId =>
      request({
        url: '/base/attr/door/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '斗门代码', key: 'projectCode' },
      { label: '斗门名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '斗门经度(°)', key: 'longitude' },
      { label: '斗门维度(°)', key: 'latitude' },
      { label: '斗门所在位置', key: 'location' },
    ],
  },
  HP040: {
    objectCategoryName: '山塘',
    api: projectId =>
      request({
        url: '/base/attr/pool/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '山塘代码', key: 'projectCode' },
      { label: '山塘名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '山塘经度(°)', key: 'longitude' },
      { label: '山塘维度(°)', key: 'latitude' },
      { label: '山塘所在位置', key: 'location' },
    ],
  },
  HP051: {
    objectCategoryName: '渠首',
    api: projectId =>
      request({
        url: '/base/attr/canal/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '渠首代码', key: 'projectCode' },
      { label: '渠首名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '渠首经度(°)', key: 'longitude' },
      { label: '渠首维度(°)', key: 'latitude' },
      { label: '渠首所在位置', key: 'location' },
    ],
  },
  HP023: {
    objectCategoryName: '隧洞',
    api: projectId =>
      request({
        url: '/base/attr/tunnel/get',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: { projectId },
      }),
    dealDataInfo: dataInfo => {
      // TODO 在此处处理数据
      return dataInfo
    },
    params: [
      { label: '隧洞代码', key: 'projectCode' },
      { label: '隧洞名称', key: 'projectName' },
      { label: '行政区划', key: 'districtFullName' },
      { label: '隧洞经度(°)', key: 'longitude' },
      { label: '隧洞维度(°)', key: 'latitude' },
      { label: '隧洞所在位置', key: 'location' },
    ],
  },
}
