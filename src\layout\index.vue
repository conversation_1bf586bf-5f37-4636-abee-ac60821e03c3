<script setup lang="ts">
  import { useSizeStore, useUserStore } from '@/store'
  import { logout } from '@/api/user'
  import { sleep } from '@/utils/index.js'

  const sizeStore = useSizeStore()
  const userStore = useUserStore()

  // 退出登录
  const onLogOutClick = () => {
    window.$dialog.warning({
      title: '退出登录',
      content: '确认退出登录？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick() {
        logout().then(async () => {
          window.$message.success('退出成功')
          userStore.$patch({ token: null })
          userStore.$patch({ user: null })

          await sleep(1500)

          window.location.href = `${import.meta.env.VITE_CAS_URL}/logout?service=${window.location.origin}`
        })
      },
      onNegativeClick() {
        $message.warning('已取消')
      },
    })
  }

  const onResize = () => {
    sizeStore.onWindowResize()
  }
</script>

<template>
  <div class="app-wrapper">
    <div class="app-wrapper-content" v-resize="onResize">
      <RouterView />
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .app-wrapper {
    position: relative;
    height: 100vh;
    width: 100vw;

    .app-wrapper-content {
      position: absolute;

      height: 100vh;
      width: 100vw;
    }
  }
</style>
